# 🔧 Troubleshooting Google OAuth2 - Mobile App "Quay suốt"

## 🚨 Vấn đề: Mobile App không redirect về Home sau OAuth2

### 📋 Checklist Debug

#### 1. **Kiểm tra Backend Logs**
```bash
# Chạy backend và xem logs
npm run dev

# Logs cần thấy khi OAuth2 thành công:
🔄 Initiating Google OAuth2 flow
🔍 Validating Google OAuth2 profile
👤 Processing user with email: <EMAIL>
✅ Existing user found: uuid-here
🎉 Google OAuth2 validation successful for user: <EMAIL>
📥 Google OAuth2 callback received
✅ Google OAuth2 successful for user: <EMAIL>
🔗 Redirecting to deep link: financeapp://auth/callback?token=***&refresh_token=***&user_email=<EMAIL>
```

#### 2. **Test Deep Link Trực Tiếp**
```bash
# Test endpoint deep link
curl -v http://localhost:8000/api/v1/auth/test-deep-link

# Hoặc mở browser:
http://localhost:8000/api/v1/auth/test-deep-link

# Expected: Redirect to financeapp://auth/callback?test=true&timestamp=...
```

#### 3. **Kiểm tra Deep Link trong Mobile App**

##### Android:
```bash
# Test deep link với adb
adb shell am start \
  -W -a android.intent.action.VIEW \
  -d "financeapp://auth/callback?token=test_token&refresh_token=test_refresh&user_email=<EMAIL>" \
  com.yourapp.package

# Kiểm tra AndroidManifest.xml
<activity android:name=".MainActivity">
  <intent-filter android:autoVerify="true">
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="financeapp" />
  </intent-filter>
</activity>
```

##### iOS:
```bash
# Test deep link với simulator
xcrun simctl openurl booted "financeapp://auth/callback?token=test_token&refresh_token=test_refresh"

# Kiểm tra Info.plist
<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleURLName</key>
    <string>financeapp</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>financeapp</string>
    </array>
  </dict>
</array>
```

#### 4. **Kiểm tra Mobile App Code**

##### React Native:
```javascript
import { Linking } from 'react-native';

// 1. Đăng ký listener khi app khởi động
useEffect(() => {
  // Xử lý deep link khi app đang chạy
  const handleUrl = (event) => {
    console.log('🔗 Deep link received:', event.url);
    handleDeepLink(event.url);
  };

  // Lắng nghe deep link
  const subscription = Linking.addEventListener('url', handleUrl);

  // Xử lý deep link khi app được mở từ deep link
  Linking.getInitialURL().then((url) => {
    if (url) {
      console.log('🔗 Initial deep link:', url);
      handleDeepLink(url);
    }
  });

  return () => subscription?.remove();
}, []);

// 2. Hàm xử lý deep link
const handleDeepLink = (url) => {
  console.log('🔍 Processing deep link:', url);
  
  if (url.startsWith('financeapp://auth/callback')) {
    const urlParts = url.split('?');
    if (urlParts.length > 1) {
      const params = new URLSearchParams(urlParts[1]);
      
      const token = params.get('token');
      const refreshToken = params.get('refresh_token');
      const userEmail = params.get('user_email');
      const error = params.get('error');
      
      console.log('🎯 Deep link params:', { token: token?.substring(0, 20) + '...', refreshToken: refreshToken?.substring(0, 20) + '...', userEmail, error });
      
      if (token && refreshToken) {
        // Đăng nhập thành công
        console.log('✅ OAuth2 success, saving tokens...');
        saveTokensAndNavigate(token, refreshToken, userEmail);
      } else if (error) {
        // Đăng nhập thất bại
        const message = params.get('message') || 'Authentication failed';
        console.log('❌ OAuth2 error:', error, message);
        Alert.alert('Đăng nhập thất bại', decodeURIComponent(message));
      }
    }
  }
};

// 3. Lưu tokens và chuyển màn hình
const saveTokensAndNavigate = async (token, refreshToken, userEmail) => {
  try {
    await AsyncStorage.setItem('accessToken', token);
    await AsyncStorage.setItem('refreshToken', refreshToken);
    await AsyncStorage.setItem('userEmail', userEmail);
    
    console.log('💾 Tokens saved successfully');
    
    // Chuyển về màn hình Home
    navigation.reset({
      index: 0,
      routes: [{ name: 'Home' }],
    });
    
    console.log('🏠 Navigated to Home');
  } catch (error) {
    console.error('❌ Error saving tokens:', error);
    Alert.alert('Lỗi', 'Không thể lưu thông tin đăng nhập');
  }
};
```

## 🐛 Các lỗi thường gặp

### 1. **Deep Link không được trigger**
```javascript
// Kiểm tra xem app có đăng ký deep link scheme không
Linking.canOpenURL('financeapp://test').then(supported => {
  console.log('Deep link supported:', supported);
});
```

### 2. **Browser không redirect về app**
- Kiểm tra browser có hỗ trợ deep link không
- Thử với browser khác (Chrome, Safari, Firefox)
- Kiểm tra mobile OS version

### 3. **App nhận deep link nhưng không navigate**
```javascript
// Debug navigation
const navigation = useNavigation();

// Thêm listener để debug navigation state
useEffect(() => {
  const unsubscribe = navigation.addListener('state', (e) => {
    console.log('Navigation state changed:', e.data.state);
  });
  return unsubscribe;
}, [navigation]);
```

### 4. **Tokens không hợp lệ**
```javascript
// Verify token sau khi nhận
const verifyToken = async (token) => {
  try {
    const response = await fetch('http://your-api.com/api/v1/users/profile', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const user = await response.json();
      console.log('✅ Token valid, user:', user);
      return true;
    } else {
      console.log('❌ Token invalid, status:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Token verification error:', error);
    return false;
  }
};
```

## 🔧 Debug Steps

### Step 1: Test Backend
```bash
# 1. Kiểm tra config
curl http://localhost:8000/api/v1/debug/google-oauth

# 2. Test deep link redirect
curl -v http://localhost:8000/api/v1/auth/test-deep-link

# 3. Test OAuth2 flow
# Mở browser: http://localhost:8000/api/v1/auth/google
```

### Step 2: Test Mobile App
```javascript
// 1. Test deep link handling
const testDeepLink = () => {
  const testUrl = 'financeapp://auth/callback?token=test_token&refresh_token=test_refresh&user_email=<EMAIL>';
  Linking.openURL(testUrl);
};

// 2. Test navigation
const testNavigation = () => {
  navigation.reset({
    index: 0,
    routes: [{ name: 'Home' }],
  });
};
```

### Step 3: End-to-End Test
1. Mở mobile app
2. Click Google login button
3. Kiểm tra browser mở đúng URL
4. Đăng nhập Google
5. Kiểm tra backend logs
6. Kiểm tra deep link redirect
7. Kiểm tra mobile app nhận deep link
8. Kiểm tra navigation

## 🚀 Solutions

### Solution 1: Fallback Mechanism
```javascript
// Thêm timeout cho OAuth2 flow
useEffect(() => {
  let oauthTimeout;
  
  const startOAuthTimeout = () => {
    oauthTimeout = setTimeout(() => {
      console.log('⏰ OAuth2 timeout, showing manual option');
      Alert.alert(
        'Đăng nhập chậm?', 
        'Bạn có thể thử đăng nhập lại hoặc sử dụng email/password',
        [
          { text: 'Thử lại', onPress: () => startGoogleLogin() },
          { text: 'Dùng Email', onPress: () => navigation.navigate('EmailLogin') }
        ]
      );
    }, 30000); // 30 seconds timeout
  };
  
  return () => {
    if (oauthTimeout) clearTimeout(oauthTimeout);
  };
}, []);
```

### Solution 2: Manual Token Input
```javascript
// Thêm option nhập token thủ công cho debug
const manualTokenInput = () => {
  Alert.prompt(
    'Debug: Nhập Token',
    'Paste token từ backend logs:',
    (token) => {
      if (token) {
        saveTokensAndNavigate(token, 'manual_refresh', '<EMAIL>');
      }
    }
  );
};
```

### Solution 3: WebView Fallback
```javascript
// Sử dụng WebView thay vì external browser
import { WebView } from 'react-native-webview';

const GoogleLoginWebView = () => {
  const handleNavigationStateChange = (navState) => {
    if (navState.url.startsWith('financeapp://')) {
      // Xử lý deep link từ WebView
      handleDeepLink(navState.url);
      return false; // Prevent WebView from navigating
    }
    return true;
  };

  return (
    <WebView
      source={{ uri: 'http://your-api.com/api/v1/auth/google' }}
      onNavigationStateChange={handleNavigationStateChange}
      onShouldStartLoadWithRequest={handleNavigationStateChange}
    />
  );
};
```

## 📊 Monitoring

Thêm analytics để track OAuth2 flow:

```javascript
// Track OAuth2 events
const trackOAuthEvent = (event, data = {}) => {
  console.log(`📊 OAuth2 Event: ${event}`, data);
  // Gửi lên analytics service
};

// Usage
trackOAuthEvent('oauth_started');
trackOAuthEvent('deep_link_received', { url });
trackOAuthEvent('tokens_saved');
trackOAuthEvent('navigation_success');
```
