import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { BaseEntity } from '@base/entity';
import { User } from '@modules/users/entity';
import { Category } from './category.entity';
import { BudgetStatus } from '../enums';

@Entity({ name: 'budgets' })
export class Budget extends BaseEntity {
    @Column({ type: 'uuid' })
    user_id: string;

    @ManyToOne(() => User, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'user_id' })
    user: User;

    @Column({ type: 'uuid' })
    category_id: string;

    @ManyToOne(() => Category, { onDelete: 'RESTRICT' })
    @JoinColumn({ name: 'category_id' })
    category: Category;

    @Column({ type: 'int' })
    month: number;

    @Column({ type: 'int' })
    year: number;

    @Column({ type: 'decimal', precision: 20, scale: 8, nullable: false, transformer: { to: (value) => value, from: (value) => parseFloat(value) } })
    amount: number;

    @Column({ type: 'decimal', precision: 20, scale: 8, nullable: false, default: 0, transformer: { to: (value) => value, from: (value) => parseFloat(value) } })
    amount_spent: number;

    @Column({
        type: 'enum',
        enum: BudgetStatus,
        default: BudgetStatus.ACTIVE,
    })
    status: BudgetStatus;

    @Column({ default: true })
    is_active: boolean;

    @Column({ length: 3, default: 'USD' })
    currency: string;
}
