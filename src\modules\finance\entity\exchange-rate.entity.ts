import { BaseEntity } from '@base/entity';
import { Column, Entity, Index } from 'typeorm';

@Entity({ name: 'exchange_rates' })
@Index(['from_currency', 'to_currency', 'effective_date'])
export class ExchangeRate extends BaseEntity {
    @Column({ length: 3 })
    from_currency: string;

    @Column({ length: 3 })
    to_currency: string;

    @Column('decimal', { precision: 15, scale: 8 })
    rate: number;

    @Column({ type: 'timestamp' })
    effective_date: Date;

    @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    fetched_at: Date;

    @Column({ default: true })
    is_active: boolean;
}
