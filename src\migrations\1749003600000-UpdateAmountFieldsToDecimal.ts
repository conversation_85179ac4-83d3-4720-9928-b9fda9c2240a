import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateAmountFieldsToDecimal1749003600000 implements MigrationInterface {
    name = 'UpdateAmountFieldsToDecimal1749003600000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Update users table - initial_balance
        await queryRunner.query(`
            ALTER TABLE "users" 
            ALTER COLUMN "initial_balance" TYPE DECIMAL(20,8) USING "initial_balance"::DECIMAL(20,8)
        `);

        // Update transactions table - amount and original_amount
        await queryRunner.query(`
            ALTER TABLE "transactions" 
            ALTER COLUMN "amount" TYPE DECIMAL(20,8) USING "amount"::DECIMAL(20,8)
        `);

        await queryRunner.query(`
            ALTER TABLE "transactions" 
            ALTER COLUMN "original_amount" TYPE DECIMAL(20,8) USING "original_amount"::DECIMAL(20,8)
        `);

        // Update budgets table - amount and amount_spent
        await queryRunner.query(`
            ALTER TABLE "budgets" 
            ALTER COLUMN "amount" TYPE DECIMAL(20,8) USING "amount"::DECIMAL(20,8)
        `);

        await queryRunner.query(`
            ALTER TABLE "budgets" 
            ALTER COLUMN "amount_spent" TYPE DECIMAL(20,8) USING "amount_spent"::DECIMAL(20,8)
        `);

        // Update goals table - target_amount and current_amount
        await queryRunner.query(`
            ALTER TABLE "goals" 
            ALTER COLUMN "target_amount" TYPE DECIMAL(20,8) USING "target_amount"::DECIMAL(20,8)
        `);

        await queryRunner.query(`
            ALTER TABLE "goals" 
            ALTER COLUMN "current_amount" TYPE DECIMAL(20,8) USING "current_amount"::DECIMAL(20,8)
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert users table
        await queryRunner.query(`
            ALTER TABLE "users" 
            ALTER COLUMN "initial_balance" TYPE BIGINT USING "initial_balance"::BIGINT
        `);

        // Revert transactions table
        await queryRunner.query(`
            ALTER TABLE "transactions" 
            ALTER COLUMN "amount" TYPE BIGINT USING "amount"::BIGINT
        `);

        await queryRunner.query(`
            ALTER TABLE "transactions" 
            ALTER COLUMN "original_amount" TYPE BIGINT USING "original_amount"::BIGINT
        `);

        // Revert budgets table
        await queryRunner.query(`
            ALTER TABLE "budgets" 
            ALTER COLUMN "amount" TYPE BIGINT USING "amount"::BIGINT
        `);

        await queryRunner.query(`
            ALTER TABLE "budgets" 
            ALTER COLUMN "amount_spent" TYPE BIGINT USING "amount_spent"::BIGINT
        `);

        // Revert goals table
        await queryRunner.query(`
            ALTER TABLE "goals" 
            ALTER COLUMN "target_amount" TYPE BIGINT USING "target_amount"::BIGINT
        `);

        await queryRunner.query(`
            ALTER TABLE "goals" 
            ALTER COLUMN "current_amount" TYPE BIGINT USING "current_amount"::BIGINT
        `);
    }
}
