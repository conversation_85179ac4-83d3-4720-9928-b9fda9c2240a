import { Type } from "class-transformer";
import { CategoryStatus, CategoryType } from "../enums";
import {
    IsUUID,
    IsNotEmpty,
    IsOptional,
    IsInt,
    Min,
    IsString,
    IsEnum,
} from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

export class CreateCategoryDto {
    @ApiProperty({ example: "Food", description: "Tên danh mục" })
    @IsString()
    @IsNotEmpty()
    name: string;

    @ApiProperty({ enum: CategoryType, example: CategoryType.EXPENSE })
    @IsEnum(CategoryType)
    type: CategoryType;
}

export class UpdateCategoryDto {
    @ApiPropertyOptional({ example: "Groceries", description: "Tên danh mục" })
    @IsOptional()
    @IsString()
    name?: string;

    @ApiPropertyOptional({
        enum: CategoryType,
        example: CategoryType.EXPENSE,
    })
    @IsOptional()
    @IsEnum(CategoryType)
    type?: CategoryType;

    @ApiPropertyOptional({
        enum: CategoryStatus,
        example: CategoryStatus.ACTIVE,
    })
    @IsOptional()
    @IsEnum(CategoryStatus)
    status?: CategoryStatus;
}

export class FindAllCategoryDto {
    @ApiPropertyOptional({ example: 1 })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    page: number = 1;

    @ApiPropertyOptional({ example: 10 })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    limit: number = 10;

    @ApiPropertyOptional({
        enum: CategoryStatus,
        example: CategoryStatus.ACTIVE,
    })
    @IsOptional()
    @IsEnum(CategoryStatus)
    status?: CategoryStatus;

    @ApiPropertyOptional({
        enum: CategoryType,
        example: CategoryType.INCOME,
    })
    @IsOptional()
    @IsEnum(CategoryType)
    type?: CategoryType;
}

export class FindOneCategoryDto {
    @ApiPropertyOptional({ example: 1 })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    page?: number = 1;

    @ApiPropertyOptional({ example: 10 })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    limit?: number = 10;
}
