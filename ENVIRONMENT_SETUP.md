# 🌍 Environment Configuration Guide

## 📋 Overview

Hệ thống ConfigService đã được cải thiện để hỗ trợ multiple environments với automatic loading và validation.

## 🗂️ Environment Files Structure

```
project-root/
├── .env                    # Default fallback
├── .env.local             # Local overrides (gitignored)
├── .env.development       # Development environment
├── .env.production        # Production environment
├── .env.test             # Test environment
└── .env.example          # Template file
```

## 🔧 Environment Loading Priority

ConfigService sẽ load các file theo thứ tự ưu tiên:

1. `.env.{NODE_ENV}` (e.g., `.env.production`)
2. `.env.local` (local overrides)
3. `.env` (default fallback)

## 🚀 Usage

### Development
```bash
# Sử dụng .env.development
npm run dev

# Hoặc
NODE_ENV=development npm start
```

### Production
```bash
# Build cho production
npm run build:production

# Chạy production với .env.production
npm run start:production

# Hoặc manual
NODE_ENV=production npm run start:prod
```

### Testing
```bash
# Sử dụng .env.test
npm run test

# Hoặc
NODE_ENV=test npm test
```

## 📄 Environment Files

### `.env.development`
```env
NODE_ENV=development
PORT=3000
DOMAIN=http://localhost:3000

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=finance_app_dev
DB_USERNAME=postgres
DB_PASSWORD=password

# JWT (shorter expiry for development)
JWT_SECRET=dev-secret-key
AC_TOKEN_EXPIRED=1h
RF_TOKEN_EXPIRED=7d

# Google OAuth (development)
GOOGLE_CLIENT_ID=your-dev-client-id
GOOGLE_CLIENT_SECRET=your-dev-secret
GOOGLE_CALLBACK_URL=http://localhost:3000/api/v1/auth/google/callback

# Mobile Deep Link
MOBILE_DEEP_LINK=myapp://oauth-callback

# Security (lower for development)
BCRYPT_ROUNDS=10

# Mail (optional for development)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your-app-password
```

### `.env.production`
```env
NODE_ENV=production
PORT=9006
DOMAIN=https://vuquangduy.io.vn

# Database
DB_HOST=your-production-db-host
DB_PORT=5432
DB_NAME=finance_app_prod
DB_USERNAME=your-prod-username
DB_PASSWORD=your-secure-prod-password

# JWT (production-ready)
JWT_SECRET=your-super-secure-production-jwt-secret-change-this
AC_TOKEN_EXPIRED=15m
RF_TOKEN_EXPIRED=7d

# Google OAuth (production)
GOOGLE_CLIENT_ID=your-prod-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-prod-client-secret
GOOGLE_CALLBACK_URL=https://vuquangduy.io.vn/api/v1/auth/google/callback

# Mobile Deep Link
MOBILE_DEEP_LINK=financeapp://auth/callback

# Security (high for production)
BCRYPT_ROUNDS=12

# Mail (production)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USER=<EMAIL>
MAIL_PASS=your-secure-app-password

# Cloudinary (production)
CLOUDINARY_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
```

### `.env.test`
```env
NODE_ENV=test
PORT=3001

# Test Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=finance_app_test
DB_USERNAME=postgres
DB_PASSWORD=password

# JWT (test)
JWT_SECRET=test-secret-key
AC_TOKEN_EXPIRED=1h
RF_TOKEN_EXPIRED=1d

# Mock values for testing
GOOGLE_CLIENT_ID=mock-client-id
GOOGLE_CLIENT_SECRET=mock-client-secret
GOOGLE_CALLBACK_URL=http://localhost:3001/api/v1/auth/google/callback

MOBILE_DEEP_LINK=myapp://oauth-callback

BCRYPT_ROUNDS=4  # Lower for faster tests
```

## ✅ Configuration Validation

ConfigService tự động validate các configuration:

### Required Fields
- `DB_HOST`, `DB_USERNAME`, `DB_PASSWORD`, `DB_NAME`
- `JWT_SECRET`

### Production Security Checks
- JWT_SECRET không được là default value
- BCRYPT_ROUNDS >= 12
- Google OAuth configuration complete

### Warnings
- Incomplete Google OAuth setup
- Missing optional services (Mail, Cloudinary)

## 📊 Configuration Logging

Khi start application, bạn sẽ thấy:

```
🔧 Loading environment: production
📄 Loading env file: .env.production
📄 Loading env file: .env.local
✅ Environment loaded successfully
📋 Configuration Summary:
   Environment: production
   Database: your-db-host:5432/finance_app_prod
   Server: http://*************:9006
   Domain: https://vuquangduy.io.vn
   Google OAuth: ✅ Configured
   Deep Link: financeapp://auth/callback
   JWT Expiry: Access=15m, Refresh=7d
```

## 🛡️ Security Best Practices

### 1. Environment Files
```bash
# Add to .gitignore
.env.local
.env.production
.env.development
.env.test

# Keep only .env.example in git
```

### 2. Production Secrets
- Use strong, unique JWT secrets
- Rotate secrets regularly
- Use environment variables in deployment
- Never commit real secrets to git

### 3. Database Security
- Use strong passwords
- Limit database user permissions
- Use SSL connections in production
- Regular backups

## 🔧 Helper Methods

ConfigService provides helper methods:

```typescript
import { config } from '@config';

// Environment checks
if (config.isDevelopment) {
  // Development-only code
}

if (config.isProduction) {
  // Production-only code
}

if (config.isTest) {
  // Test-only code
}
```

## 🚨 Troubleshooting

### Missing Configuration Error
```
❌ Missing required configuration: JWT_SECRET, DB_PASSWORD
```
**Solution**: Add missing values to your environment file

### Production Security Error
```
❌ Default JWT secret detected in production! Please change JWT_SECRET
```
**Solution**: Set a strong, unique JWT_SECRET in .env.production

### Google OAuth Warning
```
⚠️ Incomplete Google OAuth configuration
```
**Solution**: Set all three values: GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, GOOGLE_CALLBACK_URL

## 📦 Deployment

### Docker
```dockerfile
# Set environment in Dockerfile
ENV NODE_ENV=production

# Copy environment file
COPY .env.production .env.production
```

### PM2
```json
{
  "apps": [{
    "name": "finance-app",
    "script": "dist/main.js",
    "env": {
      "NODE_ENV": "production"
    }
  }]
}
```

### Systemd
```ini
[Service]
Environment=NODE_ENV=production
ExecStart=/usr/bin/node dist/main.js
```

## 🔄 Migration from Old Config

Nếu bạn đang sử dụng config cũ:

1. **Backup** file .env hiện tại
2. **Rename** .env thành .env.development
3. **Create** .env.production với production values
4. **Update** scripts trong package.json
5. **Test** với `npm run dev` và `npm run start:production`

Hệ thống mới sẽ tự động load đúng environment file dựa trên NODE_ENV!
