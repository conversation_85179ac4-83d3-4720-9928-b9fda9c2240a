import { IsN<PERSON>ber, IsOptional, IsString, IsEnum } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum Currency {
    VND = 'VND',
    USD = 'USD',
    EUR = 'EUR',
    GBP = 'GBP',
    JPY = 'JPY'
}

export class SetInitialBalanceDto {
    @ApiProperty({
        description: 'Initial balance amount',
        example: 1000000,
        type: 'number'
    })
    @IsNumber()
    initial_balance: number;

    @ApiProperty({
        description: 'Currency type',
        enum: Currency,
        example: Currency.VND,
        required: false
    })
    @IsOptional()
    @IsEnum(Currency)
    currency?: Currency;
}

export class InitialBalanceResponseDto {
    @ApiProperty({
        description: 'User ID',
        example: '123e4567-e89b-12d3-a456-426614174000'
    })
    userId: string;

    @ApiProperty({
        description: 'Initial balance amount',
        example: 1000000
    })
    initial_balance: number;

    @ApiProperty({
        description: 'Currency type',
        enum: Currency,
        example: Currency.VND
    })
    currency?: Currency;

    @ApiProperty({
        description: 'Whether initial balance has been set',
        example: true
    })
    isInitialBalance: boolean;

    @ApiProperty({
        description: 'Update timestamp',
        example: '2025-05-24T02:40:25.180Z'
    })
    updated_at: Date;
}
