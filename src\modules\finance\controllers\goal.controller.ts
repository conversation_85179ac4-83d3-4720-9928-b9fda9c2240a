import { Controller, Get, Post, Patch, Delete, Body, Param, Query } from '@nestjs/common';
import { GoalService } from '../services/goal.service';
import { CreateGoalDto, UpdateGoalDto, GoalQueryDto } from '../dto/goal.dto';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';

@ApiTags('Goals')
@ApiBearerAuth()
@Controller('goals')
export class GoalController {
    constructor(private readonly goalService: GoalService) {}

    @Post()
    create(@Body() createGoalDto: CreateGoalDto) {
        return this.goalService.create(createGoalDto);
    }

    @Get()
    findAll(@Query() query: GoalQueryDto) {
        return this.goalService.findAll(query);
    }

    @Get(':id')
    findOne(@Param('id') id: string) {
        return this.goalService.findOne(id);
    }

    @Patch(':id')
    update(
        @Param('id') id: string,
        @Body() updateGoalDto: UpdateGoalDto,
    ) {
        return this.goalService.update(id, updateGoalDto);
    }

    @Delete(':id')
    remove(@Param('id') id: string) {
        return this.goalService.remove(id);
    }
}