import { BadRequestException, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Category, Transaction } from "../entity";
import { CreateCategoryDto, FindAllCategoryDto, FindOneCategoryDto, UpdateCategoryDto } from "../dto/category.dto";
import { createPaginationMeta } from "@base/api/utils/pagination.util";

@Injectable()
export class CategoryService {
    constructor(
        @InjectRepository(Category) private readonly categoryRepo: Repository<Category>,
        @InjectRepository(Transaction) private readonly transactionRepo: Repository<Transaction>
    ) { }

    async create(dto: CreateCategoryDto, userId: string) {
        const { name, type } = dto;
        const existingCategory = await this.categoryRepo.findOne({ where: { name, type, user_id: userId } });
        if (existingCategory) throw new BadRequestException("Category already exists");
        const category = this.categoryRepo.create({
            ...dto,
            user_id: userId
        });
        return await this.categoryRepo.save(category);
    }

    async update(id: string, dto: UpdateCategoryDto, userId: string) {
        const { name, type } = dto;
        const category = await this.categoryRepo.findOne({ where: { id, user_id: userId } });

        if (!category) {
            throw new BadRequestException("Category not found");
        }

        Object.assign(category, dto);

        await this.categoryRepo.save(category);

        return category;
    }

    async delete(id: string, userId: string) {
        const category = await this.categoryRepo.findOne({ where: { id, user_id: userId } });
        if (!category) throw new BadRequestException("Category not found");
        await this.categoryRepo.delete(id);
        return true;
    }

    async getAll(query: FindAllCategoryDto, userId: string) {
        const { page = 1, limit = 10, status, type } = query;

        const qb = this.categoryRepo.createQueryBuilder('category')
            .where('category.user_id = :userId', { userId });

        if (status) {
            qb.andWhere('category.status = :status', { status });
        }

        if (type) {
            qb.andWhere('category.type = :type', { type });
        }
        qb.orderBy('category.created_at', 'DESC');
        const [items, total] = await qb
            .skip((page - 1) * limit)
            .take(limit)
            .getManyAndCount();

        return {
            data: items,
            meta: createPaginationMeta({ totalItems: total, query, itemCount: items.length }),
        };
    }



    async getOne(id: string, query: FindOneCategoryDto, userId: string) {
        const { page = 1, limit = 10 } = query;
        const category = await this.categoryRepo.findOne({
            where: { id, user_id: userId }
        });

        if (!category) {
            throw new BadRequestException("Category not found");
        }

        const [transactions, total] = await this.transactionRepo.findAndCount({
            where: { category_id: id },
            order: { transaction_date: 'DESC' },
            take: limit,
            skip: (page - 1) * limit,
        });

        return {
            data: {
                category,
                transactions
            },
            meta:
                createPaginationMeta({
                    itemCount: transactions.length,
                    totalItems: total,
                    query: { page, limit }
                }),

        };
    }


}
