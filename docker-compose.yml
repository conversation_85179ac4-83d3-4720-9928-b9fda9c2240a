version: '3.8'

services:
  finance-app:
    build: .
    ports:
      - "9005:9005"
    environment:
      - NODE_ENV=production
      - PORT=9005
      - DOMAIN=https://vuquangduy.online
    env_file:
      - .env.production
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    depends_on:
      - postgres
    networks:
      - finance-network

  postgres:
    image: postgres:15-alpine
    container_name: finance_postgres
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    networks:
      - finance-network

volumes:
  pgdata:

networks:
  finance-network:
    driver: bridge
