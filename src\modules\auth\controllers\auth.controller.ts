import { Body, Controller, Get, Post, Req, Res, <PERSON>Guards, <PERSON>gger, Query, HttpStatus } from "@nestjs/common";
import { Public } from "../jwt";
import { LoginDto, RegisterDto, ResetPasswordDto, VerifyOtpDto } from "../dto";
import { AuthService } from "../services/auth.service";
import { RefreshTokenDto } from "../dto/refresh-token.dto";
import { AuthGuard } from "@nestjs/passport";
import { ForgotPasswordDto } from "../dto/forgot-password.dto";
import { Response, Request } from 'express';
import { config } from '@config';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { GoogleOAuthCallbackDto, GoogleOAuthResponseDto } from '../dto/google-oauth.dto';
import { OAuthCallbackQuery } from '../interfaces/google-oauth.interface';

@ApiTags('Authentication')
@Controller("/auth")
@Public()
export class AuthController {
    private readonly logger = new Logger(AuthController.name);

    constructor(private readonly authService: AuthService) { }

    @Post("/register")
    @ApiOperation({ summary: 'Register new user' })
    async register(@Body() body: RegisterDto) {
        return await this.authService.register(body);
    }

    @Post("/login")
    @ApiOperation({ summary: 'Login user' })
    async login(@Body() body: LoginDto) {
        return await this.authService.login(body);
    }

    @Post('refresh-token')
    @ApiOperation({ summary: 'Refresh access token' })
    async refresh(@Body() body: RefreshTokenDto) {
        return this.authService.refreshToken(body);
    }

    @Post('forgot-password')
    @ApiOperation({ summary: 'Send forgot password OTP' })
    async forgotPassword(@Body() body: ForgotPasswordDto) {
        return this.authService.forgotPassword(body);
    }

    @Post('verify-otp')
    @ApiOperation({ summary: 'Verify OTP code' })
    async verifyOtp(@Body() body: VerifyOtpDto) {
        return this.authService.verifyOtp(body);
    }

    @Post('reset-password')
    @ApiOperation({ summary: 'Reset password with OTP' })
    async resetPassword(@Body() body: ResetPasswordDto) {
        return this.authService.resetPassword(body);
    }

    @Get('google')
    @UseGuards(AuthGuard('google'))
    @ApiOperation({
        summary: 'Initiate Google OAuth2 login',
        description: 'Redirects user to Google OAuth2 consent screen. Mobile app should open this URL in browser.'
    })
    @ApiResponse({
        status: 302,
        description: 'Redirects to Google OAuth2 consent screen'
    })
    async googleAuth(@Req() req: Request) {
        this.logger.log('🔄 Initiating Google OAuth2 flow');
        this.logger.debug(`Request headers:`, {
            userAgent: req.headers['user-agent'],
            origin: req.headers.origin,
            referer: req.headers.referer
        });
        // Passport will handle the redirect to Google
        // This method will not be reached due to the redirect
    }

    @Get('google/callback')
    @UseGuards(AuthGuard('google'))
    @ApiOperation({
        summary: 'Google OAuth2 callback',
        description: 'Handles Google OAuth2 callback, creates JWT token and redirects to mobile app via deep link'
    })
    @ApiResponse({
        status: 302,
        description: 'Redirects to mobile app with JWT token via deep link'
    })
    @ApiResponse({
        status: 400,
        description: 'Bad request - OAuth2 error or missing user data'
    })
    async googleAuthCallback(@Req() req: Request, @Res() res: Response) {
        const startTime = Date.now();
        const requestId = this.generateRequestId();

        try {
            this.logger.log(`📥 [${requestId}] Google OAuth2 callback received`);
            this.logger.debug(`[${requestId}] Callback query params:`, req.query);
            this.logger.debug(`[${requestId}] Request headers:`, {
                userAgent: req.headers['user-agent'],
                referer: req.headers.referer,
                origin: req.headers.origin
            });

            // Validate callback parameters
            this.validateCallbackParams(req.query, requestId);

            if (!req.user) {
                this.logger.error(`❌ [${requestId}] No user data received from Google OAuth2`);
                return this.redirectWithError(res, 'no_user_data', 'Authentication failed', requestId);
            }

            this.logger.debug(`[${requestId}] User from Google:`, {
                id: (req.user as any).id,
                email: (req.user as any).email,
                provider: (req.user as any).provide
            });

            // Process Google login and generate JWT
            const result = await this.authService.googleLogin(req);
            const { accessToken, refreshToken, user } = result.data;

            this.logger.log(`✅ [${requestId}] Google OAuth2 successful for user: ${user.email} (${Date.now() - startTime}ms)`);

            // Create deep link with environment-specific scheme
            const redirectUrl = this.getRedirectUrl(accessToken, refreshToken, user.email);
            this.logger.log(`🔗 [${requestId}] Redirecting to: ${redirectUrl.replace(/accessToken=[^&]*/, 'accessToken=***').replace(/refreshToken=[^&]*/, 'refreshToken=***')}`);

            // Set security headers
            this.setSecurityHeaders(res);

            return res.redirect(302, redirectUrl);

        } catch (error) {
            this.logger.error(`❌ [${requestId}] Google OAuth2 callback error:`, error);
            this.logger.error(`[${requestId}] Error stack:`, error.stack);

            const errorMessage = this.sanitizeErrorMessage(error.message);
            return this.redirectWithError(res, 'auth_failed', errorMessage, requestId);
        }
    }

    // Helper methods
    private generateRequestId(): string {
        return `oauth_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    private validateCallbackParams(query: any, requestId: string): void {
        if (query.error) {
            this.logger.error(`❌ [${requestId}] OAuth error from Google:`, {
                error: query.error,
                errorDescription: query.error_description
            });
            throw new Error(`Google OAuth error: ${query.error_description || query.error}`);
        }

        if (!query.code) {
            this.logger.error(`❌ [${requestId}] No authorization code in callback`);
            throw new Error('No authorization code received from Google');
        }
    }

    private setSecurityHeaders(res: Response): void {
        res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        res.setHeader('Pragma', 'no-cache');
        res.setHeader('Expires', '0');
        res.setHeader('X-Content-Type-Options', 'nosniff');
        res.setHeader('X-Frame-Options', 'DENY');
        res.setHeader('X-XSS-Protection', '1; mode=block');
    }

    private sanitizeErrorMessage(message: string): string {
        if (!message) return 'Unknown error occurred';

        // Remove sensitive information
        return message
            .replace(/token/gi, '***')
            .replace(/secret/gi, '***')
            .replace(/password/gi, '***')
            .replace(/key/gi, '***')
            .substring(0, 200); // Limit length
    }

    private getRedirectUrl(accessToken: string, refreshToken: string, userEmail?: string): string {
        const isDevelopment = config.NODE_ENV === 'development';

        const params = new URLSearchParams({
            accessToken,
            refreshToken,
            status: 'success'
        });

        if (userEmail) {
            params.set('user_email', userEmail);
        }

        if (isDevelopment) {
            // Expo Go scheme for development
            return `${config.EXPO_SCHEME}://${config.EXPO_HOST}/--/auth/callback?${params.toString()}`;
        } else {
            // Custom scheme for production
            return `${config.MOBILE_DEEP_LINK}?${params.toString()}`;
        }
    }

    private getErrorRedirectUrl(errorMessage: string): string {
        const isDevelopment = config.NODE_ENV === 'development';

        const params = new URLSearchParams({
            status: 'error',
            message: errorMessage
        });

        if (isDevelopment) {
            // Expo Go scheme for development
            return `${config.EXPO_SCHEME}://${config.EXPO_HOST}/--/auth/callback?${params.toString()}`;
        } else {
            // Custom scheme for production
            return `${config.MOBILE_DEEP_LINK}?${params.toString()}`;
        }
    }

    private redirectWithError(
        res: Response,
        _errorType: string,
        errorMessage: string,
        requestId: string
    ): void {
        const errorRedirectUrl = this.getErrorRedirectUrl(errorMessage);
        this.logger.log(`🔗 [${requestId}] Error redirect to: ${errorRedirectUrl.replace(/message=[^&]*/, 'message=***')}`);

        this.setSecurityHeaders(res);
        res.redirect(302, errorRedirectUrl);
    }
}