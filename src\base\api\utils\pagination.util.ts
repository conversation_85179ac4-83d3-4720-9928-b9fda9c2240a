

interface PaginationMetaInput {
    totalItems: number;
    query: { page?: number; limit?: number };
    itemCount: number;
}

export function createPaginationMeta({
    totalItems,
    query,
    itemCount,
}: PaginationMetaInput) {
    const currentPage = Number(query.page) || 1;
    const itemPerPage = Number(query.limit) || 10;

    return {
        itemCount,
        currentPage,
        pageSize: itemPerPage,
        totalPages: Math.ceil(totalItems / itemPerPage),

    };
}

