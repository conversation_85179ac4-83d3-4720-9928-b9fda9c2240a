import { DataSource } from 'typeorm';
import { Budget } from '../modules/finance/entity/budget.entity';
import { Transaction } from '../modules/finance/entity/transaction.entity';
import { TransactionType } from '../modules/finance/enums';
import typeOrmConfig from '../config/typeorm.config';

async function syncBudgetSpent() {
    console.log('🔄 Starting budget spent synchronization...');
    
    const dataSource = typeOrmConfig;
    await dataSource.initialize();
    
    try {
        const budgetRepo = dataSource.getRepository(Budget);
        const transactionRepo = dataSource.getRepository(Transaction);
        
        // Get all active budgets
        const budgets = await budgetRepo.find({
            where: { is_active: true },
            relations: ['category']
        });
        
        console.log(`📊 Found ${budgets.length} active budgets to sync`);
        
        let updatedCount = 0;
        
        for (const budget of budgets) {
            // Calculate actual spent amount from transactions
            const startDate = new Date(budget.year, budget.month - 1, 1);
            const endDate = new Date(budget.year, budget.month, 0, 23, 59, 59);
            
            const result = await transactionRepo
                .createQueryBuilder('transaction')
                .select('SUM(transaction.amount)', 'total')
                .where('transaction.user_id = :userId', { userId: budget.user_id })
                .andWhere('transaction.category_id = :categoryId', { categoryId: budget.category_id })
                .andWhere('transaction.type = :type', { type: TransactionType.EXPENSE })
                .andWhere('transaction.transaction_date BETWEEN :startDate AND :endDate', { startDate, endDate })
                .getRawOne();
            
            const actualSpent = parseInt(result?.total || '0');
            
            // Update budget if amount_spent is different
            if (budget.amount_spent !== actualSpent) {
                console.log(`📝 Updating budget ${budget.id} (${budget.category?.name}): ${budget.amount_spent} → ${actualSpent}`);
                
                budget.amount_spent = actualSpent;
                await budgetRepo.save(budget);
                updatedCount++;
            }
        }
        
        console.log(`✅ Synchronization completed! Updated ${updatedCount} budgets.`);
        
    } catch (error) {
        console.error('❌ Error during synchronization:', error);
        throw error;
    } finally {
        await dataSource.destroy();
    }
}

// Run the script
if (require.main === module) {
    syncBudgetSpent()
        .then(() => {
            console.log('🎉 Budget spent synchronization finished successfully!');
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 Budget spent synchronization failed:', error);
            process.exit(1);
        });
}

export { syncBudgetSpent };
