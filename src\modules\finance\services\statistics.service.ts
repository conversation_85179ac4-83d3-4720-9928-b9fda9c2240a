import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Transaction } from '../entity/transaction.entity';
import { Category } from '../entity/category.entity';
import {
    IncomeExpenseChartQueryDto,
    CategoryAnalysisQueryDto,
    FinancialTrendsQueryDto,
    ComparisonQueryDto,
    IncomeExpenseChartDto,
    CategoryAnalysisDto,
    FinancialTrendsDto,
    ComparisonDto
} from '../dto/statistics.dto';
import {
    ChartDataPoint,
    TrendData,
    ComparisonPeriod,
    CategoryData,
    FinancialTrendPoint
} from '../interfaces/statistics.interface';

@Injectable()
export class StatisticsService {
    constructor(
        @InjectRepository(Transaction)
        private readonly transactionRepository: Repository<Transaction>,
        @InjectRepository(Category)
        private readonly categoryRepository: Repository<Category>,
    ) {}

    async getIncomeExpenseChart(
        userId: string,
        queryDto: IncomeExpenseChartQueryDto
    ): Promise<IncomeExpenseChartDto> {
        const { period, startDate, endDate } = queryDto;
        
        // Determine date range
        const dateRange = this.getDateRange(period, startDate, endDate);
        
        // Build query
        const query = this.transactionRepository
            .createQueryBuilder('transaction')
            .where('transaction.user_id = :userId', { userId })
            .andWhere('transaction.transaction_date >= :startDate', { startDate: dateRange.start })
            .andWhere('transaction.transaction_date <= :endDate', { endDate: dateRange.end });

        const transactions = await query.getMany();

        // Group data by period
        const groupedData = this.groupTransactionsByPeriod(transactions, period);

        // Convert to array and sort by time (newest first for week)
        const sortedData = Array.from(groupedData.entries()).sort((a, b) => {
            if (period === 'week') {
                // Sort by week order: This Week, Last Week, 2 Weeks Ago, etc.
                const getWeekOrder = (label: string) => {
                    if (label === 'This Week') return 0;
                    if (label === 'Last Week') return 1;
                    const match = label.match(/(\d+) Weeks Ago/);
                    return match ? parseInt(match[1]) : 999;
                };
                return getWeekOrder(a[0]) - getWeekOrder(b[0]);
            }
            return a[0].localeCompare(b[0]);
        });

        // Format for chart
        const labels: string[] = [];
        const incomeData: number[] = [];
        const expenseData: number[] = [];
        let totalIncome = 0;
        let totalExpense = 0;

        sortedData.forEach(([label, data]) => {
            labels.push(label);
            incomeData.push(data.income);
            expenseData.push(data.expense);
            totalIncome += data.income;
            totalExpense += data.expense;
        });

        return {
            labels,
            incomeData,
            expenseData,
            totalIncome,
            totalExpense,
            netAmount: totalIncome - totalExpense
        };
    }

    async getCategoryAnalysis(
        userId: string,
        queryDto: CategoryAnalysisQueryDto
    ): Promise<CategoryAnalysisDto> {
        const { period, type, month, year } = queryDto;
        
        // Determine date range
        const dateRange = this.getCategoryAnalysisDateRange(period, month, year);
        
        // Build query
        let query = this.transactionRepository
            .createQueryBuilder('transaction')
            .leftJoinAndSelect('transaction.category', 'category')
            .where('transaction.user_id = :userId', { userId })
            .andWhere('transaction.transaction_date >= :startDate', { startDate: dateRange.start })
            .andWhere('transaction.transaction_date <= :endDate', { endDate: dateRange.end });

        if (type) {
            query = query.andWhere('transaction.type = :type', { type });
        }

        const transactions = await query.getMany();

        // Group by category
        const categoryMap = new Map<string, { amount: number; categoryName: string }>();
        let totalAmount = 0;

        transactions.forEach(transaction => {
            const categoryId = transaction.category?.id || 'uncategorized';
            const categoryName = transaction.category?.name || 'Uncategorized';
            const amount = Math.abs(transaction.amount);

            if (categoryMap.has(categoryId)) {
                categoryMap.get(categoryId)!.amount += amount;
            } else {
                categoryMap.set(categoryId, { amount, categoryName });
            }
            totalAmount += amount;
        });

        // Convert to array and calculate percentages
        const categories: CategoryData[] = Array.from(categoryMap.entries()).map(([categoryId, data]) => ({
            categoryId,
            categoryName: data.categoryName,
            amount: data.amount,
            percentage: totalAmount > 0 ? Math.round((data.amount / totalAmount) * 100 * 100) / 100 : 0,
            color: this.generateCategoryColor(categoryId)
        }));

        // Sort by amount descending
        categories.sort((a, b) => b.amount - a.amount);

        return {
            categories,
            totalAmount,
            period: this.formatPeriodString(period, month, year)
        };
    }

    async getFinancialTrends(
        userId: string,
        queryDto: FinancialTrendsQueryDto
    ): Promise<FinancialTrendsDto> {
        const { period, limit = period === 'month' ? 12 : 5 } = queryDto;
        
        // Get date ranges for each period
        const periods = this.generatePeriods(period, limit);
        const trends: FinancialTrendPoint[] = [];

        for (const periodInfo of periods) {
            const query = this.transactionRepository
                .createQueryBuilder('transaction')
                .where('transaction.user_id = :userId', { userId })
                .andWhere('transaction.transaction_date >= :startDate', { startDate: periodInfo.start })
                .andWhere('transaction.transaction_date <= :endDate', { endDate: periodInfo.end });

            const transactions = await query.getMany();

            const income = transactions
                .filter(t => t.type === 'income')
                .reduce((sum, t) => sum + t.amount, 0);

            const expense = transactions
                .filter(t => t.type === 'expense')
                .reduce((sum, t) => sum + Math.abs(t.amount), 0);

            const balance = income - expense;

            trends.push({
                period: periodInfo.label,
                income,
                expense,
                balance,
                growth: 0 // Will calculate after all data is collected
            });
        }

        // Calculate growth rates and moving averages
        this.calculateGrowthRates(trends);
        this.calculateMovingAverages(trends);

        const overallGrowth = this.calculateOverallGrowth(trends);
        const averageBalance = trends.reduce((sum, t) => sum + t.balance, 0) / trends.length;

        return {
            trends,
            overallGrowth,
            averageBalance: Math.round(averageBalance)
        };
    }

    async getComparison(
        userId: string,
        queryDto: ComparisonQueryDto
    ): Promise<ComparisonDto> {
        const { periods, periodType } = queryDto;
        
        if (periods.length > 4) {
            throw new Error('Maximum 4 periods allowed for comparison');
        }

        const comparisonData: ComparisonPeriod[] = [];

        for (const period of periods) {
            const dateRange = this.parsePeriodString(period, periodType);
            
            const query = this.transactionRepository
                .createQueryBuilder('transaction')
                .where('transaction.user_id = :userId', { userId })
                .andWhere('transaction.transaction_date >= :startDate', { startDate: dateRange.start })
                .andWhere('transaction.transaction_date <= :endDate', { endDate: dateRange.end });

            const transactions = await query.getMany();

            const income = transactions
                .filter(t => t.type === 'income')
                .reduce((sum, t) => sum + t.amount, 0);

            const expense = transactions
                .filter(t => t.type === 'expense')
                .reduce((sum, t) => sum + Math.abs(t.amount), 0);

            comparisonData.push({
                period,
                income,
                expense,
                balance: income - expense,
                transactionCount: transactions.length
            });
        }

        // Find best and worst periods
        const bestPeriod = comparisonData.reduce((best, current) => 
            current.balance > best.balance ? current : best
        ).period;

        const worstPeriod = comparisonData.reduce((worst, current) => 
            current.balance < worst.balance ? current : worst
        ).period;

        return {
            periods: comparisonData,
            bestPeriod,
            worstPeriod
        };
    }

    // Helper methods
    private getDateRange(period: string, startDate?: string, endDate?: string) {
        if (startDate && endDate) {
            return {
                start: new Date(startDate),
                end: new Date(endDate)
            };
        }

        const now = new Date();
        const start = new Date();

        switch (period) {
            case 'week':
                // Lấy 4 tuần gần nhất (28 ngày)
                start.setDate(now.getDate() - 28);
                break;
            case 'month':
                start.setMonth(now.getMonth() - 1);
                break;
            case 'year':
                start.setFullYear(now.getFullYear() - 1);
                break;
        }

        return { start, end: now };
    }

    private groupTransactionsByPeriod(transactions: Transaction[], period: string): Map<string, { income: number; expense: number }> {
        const grouped = new Map<string, { income: number; expense: number }>();

        transactions.forEach(transaction => {
            const label = this.getPeriodLabel(transaction.transaction_date, period);
            
            if (!grouped.has(label)) {
                grouped.set(label, { income: 0, expense: 0 });
            }

            const data = grouped.get(label)!;
            if (transaction.type === 'income') {
                data.income += transaction.amount;
            } else {
                data.expense += Math.abs(transaction.amount);
            }
        });

        return grouped;
    }

    private getPeriodLabel(date: Date, period: string): string {
        switch (period) {
            case 'week':
                // Tính tuần dựa trên khoảng cách từ hôm nay
                const now = new Date();
                const diffTime = now.getTime() - date.getTime();
                const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
                const weekNumber = Math.floor(diffDays / 7);

                if (weekNumber === 0) {
                    return 'This Week';
                } else if (weekNumber === 1) {
                    return 'Last Week';
                } else {
                    return `${weekNumber + 1} Weeks Ago`;
                }
            case 'month':
                return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
            case 'year':
                return date.getFullYear().toString();
            default:
                return date.toLocaleDateString();
        }
    }

    private getCategoryAnalysisDateRange(period: string, month?: number, year?: number) {
        const now = new Date();
        const currentYear = year || now.getFullYear();
        const currentMonth = month || now.getMonth() + 1;

        if (period === 'month') {
            const start = new Date(currentYear, currentMonth - 1, 1);
            const end = new Date(currentYear, currentMonth, 0, 23, 59, 59);
            return { start, end };
        } else {
            const start = new Date(currentYear, 0, 1);
            const end = new Date(currentYear, 11, 31, 23, 59, 59);
            return { start, end };
        }
    }

    private formatPeriodString(period: string, month?: number, year?: number): string {
        const currentYear = year || new Date().getFullYear();
        const currentMonth = month || new Date().getMonth() + 1;

        if (period === 'month') {
            return `${currentYear}-${currentMonth.toString().padStart(2, '0')}`;
        } else {
            return currentYear.toString();
        }
    }

    private generateCategoryColor(categoryId: string): string {
        // Generate consistent colors based on category ID
        const colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
        ];
        const hash = categoryId.split('').reduce((a, b) => {
            a = ((a << 5) - a) + b.charCodeAt(0);
            return a & a;
        }, 0);
        return colors[Math.abs(hash) % colors.length];
    }

    private generatePeriods(period: string, limit: number) {
        const periods = [];
        const now = new Date();

        for (let i = limit - 1; i >= 0; i--) {
            if (period === 'month') {
                const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
                const start = new Date(date.getFullYear(), date.getMonth(), 1);
                const end = new Date(date.getFullYear(), date.getMonth() + 1, 0, 23, 59, 59);
                const label = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`;
                periods.push({ start, end, label });
            } else {
                const year = now.getFullYear() - i;
                const start = new Date(year, 0, 1);
                const end = new Date(year, 11, 31, 23, 59, 59);
                periods.push({ start, end, label: year.toString() });
            }
        }

        return periods;
    }

    private calculateGrowthRates(trends: FinancialTrendPoint[]) {
        for (let i = 1; i < trends.length; i++) {
            const current = trends[i];
            const previous = trends[i - 1];
            
            if (previous.balance !== 0) {
                current.growth = Math.round(((current.balance - previous.balance) / Math.abs(previous.balance)) * 100 * 100) / 100;
            } else {
                current.growth = current.balance > 0 ? 100 : 0;
            }
        }
    }

    private calculateMovingAverages(trends: FinancialTrendPoint[], window: number = 3) {
        for (let i = window - 1; i < trends.length; i++) {
            const sum = trends.slice(i - window + 1, i + 1).reduce((acc, trend) => acc + trend.balance, 0);
            trends[i].movingAverage = Math.round(sum / window);
        }
    }

    private calculateOverallGrowth(trends: FinancialTrendPoint[]): number {
        if (trends.length < 2) return 0;
        
        const first = trends[0];
        const last = trends[trends.length - 1];
        
        if (first.balance === 0) return last.balance > 0 ? 100 : 0;
        
        return Math.round(((last.balance - first.balance) / Math.abs(first.balance)) * 100 * 100) / 100;
    }

    private parsePeriodString(period: string, periodType: string) {
        if (periodType === 'month') {
            const [year, month] = period.split('-').map(Number);
            const start = new Date(year, month - 1, 1);
            const end = new Date(year, month, 0, 23, 59, 59);
            return { start, end };
        } else {
            const year = parseInt(period);
            const start = new Date(year, 0, 1);
            const end = new Date(year, 11, 31, 23, 59, 59);
            return { start, end };
        }
    }
}
