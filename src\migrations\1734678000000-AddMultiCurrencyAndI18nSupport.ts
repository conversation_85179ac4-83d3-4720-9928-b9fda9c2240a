import { MigrationInterface, QueryRunner } from "typeorm";

export class AddMultiCurrencyAndI18nSupport1734678000000 implements MigrationInterface {
    name = 'AddMultiCurrencyAndI18nSupport1734678000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create exchange_rates table
        await queryRunner.query(`
            CREATE TABLE "exchange_rates" (
                "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                "deleted_at" TIMESTAMP,
                "from_currency" character varying(3) NOT NULL,
                "to_currency" character varying(3) NOT NULL,
                "rate" numeric(15,8) NOT NULL,
                "effective_date" TIMESTAMP NOT NULL,
                "fetched_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "is_active" boolean NOT NULL DEFAULT true,
                CONSTRAINT "PK_exchange_rates" PRIMARY KEY ("id")
            )
        `);

        // Create index on exchange_rates
        await queryRunner.query(`
            CREATE INDEX "IDX_exchange_rates_currency_date" 
            ON "exchange_rates" ("from_currency", "to_currency", "effective_date")
        `);

        // Add user preference columns
        await queryRunner.query(`
            ALTER TABLE "users" 
            ADD COLUMN "preferred_language" character varying(5) NOT NULL DEFAULT 'en',
            ADD COLUMN "preferred_currency" character varying(3) NOT NULL DEFAULT 'USD',
            ADD COLUMN "timezone" character varying(50) NOT NULL DEFAULT 'UTC'
        `);

        // Add transaction currency columns
        await queryRunner.query(`
            ALTER TABLE "transactions" 
            ADD COLUMN "original_currency" character varying(3) NOT NULL DEFAULT 'USD',
            ADD COLUMN "original_amount" bigint,
            ADD COLUMN "exchange_rate" numeric(15,8) NOT NULL DEFAULT 1.0
        `);

        // Add budget currency column
        await queryRunner.query(`
            ALTER TABLE "budgets" 
            ADD COLUMN "currency" character varying(3) NOT NULL DEFAULT 'USD'
        `);

        // Add goal currency column
        await queryRunner.query(`
            ALTER TABLE "goals" 
            ADD COLUMN "currency" character varying(3) NOT NULL DEFAULT 'USD'
        `);

        // Insert initial exchange rates (USD as base)
        await queryRunner.query(`
            INSERT INTO "exchange_rates" ("from_currency", "to_currency", "rate", "effective_date", "is_active")
            VALUES 
                ('USD', 'VND', 24000.0, NOW(), true),
                ('VND', 'USD', 0.0000417, NOW(), true),
                ('USD', 'CNY', 7.2, NOW(), true),
                ('CNY', 'USD', 0.1389, NOW(), true),
                ('VND', 'CNY', 0.0003, NOW(), true),
                ('CNY', 'VND', 3333.33, NOW(), true)
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove goal currency column
        await queryRunner.query(`ALTER TABLE "goals" DROP COLUMN "currency"`);

        // Remove budget currency column
        await queryRunner.query(`ALTER TABLE "budgets" DROP COLUMN "currency"`);

        // Remove transaction currency columns
        await queryRunner.query(`
            ALTER TABLE "transactions" 
            DROP COLUMN "exchange_rate",
            DROP COLUMN "original_amount",
            DROP COLUMN "original_currency"
        `);

        // Remove user preference columns
        await queryRunner.query(`
            ALTER TABLE "users" 
            DROP COLUMN "timezone",
            DROP COLUMN "preferred_currency",
            DROP COLUMN "preferred_language"
        `);

        // Drop exchange_rates table
        await queryRunner.query(`DROP INDEX "IDX_exchange_rates_currency_date"`);
        await queryRunner.query(`DROP TABLE "exchange_rates"`);
    }
}
