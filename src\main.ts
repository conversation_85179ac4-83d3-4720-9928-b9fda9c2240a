import { HttpAdapterHost, NestFactory, Reflector } from "@nestjs/core";
import { AppModule } from "./app.module";
import * as morgan from 'morgan';
import helmet from 'helmet';
import { Val<PERSON><PERSON>Pip<PERSON>, Logger } from "@nestjs/common";
import { useContainer } from "class-validator";
import { AllExceptionsFilter } from "@base/api/exception";
import { InitSwagger } from "@base/swagger";
import { config } from "@config";
import { CustomResponseInterceptor } from "@base/api/response/custom.response";
import { configureCloudinary } from "@modules/upload/configs/cloudinary.config";
import { LocalizationMiddleware } from "@base/middleware/localization.middleware";
import { CurrencyInterceptor } from "@base/interceptors/currency.interceptor";

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const port = config.PORT;
  const reflector = app.get(Reflector)
  const logger = new Logger('HTTP');

  app.setGlobalPrefix("api/v1");

  // Fix duplicate prefix middleware
  app.use((req, res, next) => {
    // Check for duplicate /api/v1/api/v1 prefix
    if (req.url.startsWith('/api/v1/api/v1/')) {
      const correctedUrl = req.url.replace('/api/v1/api/v1/', '/api/v1/');
      logger.warn(`🔧 Fixing duplicate prefix: ${req.url} → ${correctedUrl}`);
      req.url = correctedUrl;
    }
    next();
  });

  // Custom request logging middleware
  app.use((req, res, next) => {
    logger.log(`📨 Incoming ${req.method} request to ${req.url}`);
    logger.debug(`Headers: ${JSON.stringify(req.headers)}`);

    // Log request body for POST/PUT/PATCH
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      logger.debug(`Body: ${JSON.stringify(req.body)}`);
    }

    // Log response
    const originalSend = res.send;
    res.send = function (body) {
      logger.log(`📤 Response for ${req.method} ${req.url} - Status: ${res.statusCode}`);
      logger.debug(`Response body: ${body}`);
      return originalSend.call(this, body);
    };

    next();
  });

  // middleware
  app.use(morgan('dev'));
  app.use(helmet({
    crossOriginEmbedderPolicy: false,
    crossOriginResourcePolicy: { policy: "cross-origin" },
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https:"],
        scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'", "https:", "http:"],
        fontSrc: ["'self'", "https:", "data:"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'self'"],
      },
    },
  }));

  // CORS
  app.enableCors(config.CORS);
  logger.log(`🔓 CORS enabled with config: ${JSON.stringify(config.CORS)}`);

  // Fix for ngrok OAuth issues
  app.use((req, res, next) => {
    if (req.headers['ngrok-skip-browser-warning']) {
      delete req.headers['ngrok-skip-browser-warning'];
    }
    // Set proper host header for ngrok
    if (req.headers['x-forwarded-host']) {
      req.headers.host = req.headers['x-forwarded-host'];
    }
    next();
  });

  // Handle preflight OPTIONS requests
  app.use((req, res, next) => {
    if (req.method === 'OPTIONS') {
      res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
      res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,PATCH,DELETE,OPTIONS,HEAD');
      res.header('Access-Control-Allow-Headers', 'Content-Type,Authorization,Accept,Origin,X-Requested-With');
      res.header('Access-Control-Allow-Credentials', 'true');
      res.header('Access-Control-Max-Age', '86400');
      return res.status(204).end();
    }
    next();
  });

  // Open API Swagger
  InitSwagger(app);

  // pipe validate
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    forbidNonWhitelisted: true,
    transform: true
  }))

  // Config
  configureCloudinary()

  // Interceptors
  app.useGlobalInterceptors(new CustomResponseInterceptor(reflector));

  // Note: CurrencyInterceptor and LocalizationMiddleware will be added after services are available

  // validate
  useContainer(app.select(AppModule), { fallbackOnErrors: true });

  // Exception
  const httpAdapterHost = app.get(HttpAdapterHost)
  app.useGlobalFilters(new AllExceptionsFilter(httpAdapterHost.httpAdapter),);

  await app.listen(port ?? 3001, '0.0.0.0');
  logger.log(`🚀 Application is running on: ${await app.getUrl()}`);
  logger.log(`📡 Local IP: ${config.LOCAL_IP}`);
  logger.log(`🌐 Public IP: ${config.PUBLIC_IP}`);
}
bootstrap();