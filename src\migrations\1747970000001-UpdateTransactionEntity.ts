import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateTransactionEntity1747970000001 implements MigrationInterface {
    name = 'UpdateTransactionEntity1747970000001'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Make category_id nullable
        await queryRunner.query(`ALTER TABLE "transactions" ALTER COLUMN "category_id" DROP NOT NULL`);
        
        // Update foreign key constraint to SET NULL on delete
        await queryRunner.query(`
            ALTER TABLE "transactions" 
            DROP CONSTRAINT IF EXISTS "FK_transactions_category_id"
        `);
        await queryRunner.query(`
            ALTER TABLE "transactions" 
            ADD CONSTRAINT "FK_transactions_category_id" 
            FOREIGN KEY ("category_id") 
            REFERENCES "categories"("id") 
            ON DELETE SET NULL
        `);

        // Make other columns nullable
        await queryRunner.query(`ALTER TABLE "transactions" ALTER COLUMN "description" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "transactions" ALTER COLUMN "notes" DROP NOT NULL`);
        await queryRunner.query(`ALTER TABLE "transactions" ALTER COLUMN "image_url" DROP NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert foreign key constraint
        await queryRunner.query(`
            ALTER TABLE "transactions" 
            DROP CONSTRAINT IF EXISTS "FK_transactions_category_id"
        `);
        await queryRunner.query(`
            ALTER TABLE "transactions" 
            ADD CONSTRAINT "FK_transactions_category_id" 
            FOREIGN KEY ("category_id") 
            REFERENCES "categories"("id") 
            ON DELETE RESTRICT
        `);

        // Make columns NOT NULL again
        await queryRunner.query(`ALTER TABLE "transactions" ALTER COLUMN "category_id" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "transactions" ALTER COLUMN "description" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "transactions" ALTER COLUMN "notes" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "transactions" ALTER COLUMN "image_url" SET NOT NULL`);
    }
} 