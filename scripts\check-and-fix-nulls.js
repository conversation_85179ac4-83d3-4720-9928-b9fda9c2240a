const { Client } = require('pg');

async function checkAndFixNulls() {
    const client = new Client({
        host: 'localhost',
        port: 5432,
        database: 'postgres',
        user: 'postgres',
        password: 'Duy0509@'
    });

    try {
        await client.connect();
        console.log('✅ Connected to database');

        // Check for NULL values in each table
        console.log('\n🔍 Checking for NULL values...');
        
        // Check budgets table
        const budgetsNulls = await client.query(`
            SELECT COUNT(*) as count FROM budgets WHERE amount IS NULL OR amount_spent IS NULL
        `);
        console.log(`📊 Budgets with NULL amounts: ${budgetsNulls.rows[0].count}`);

        // Check transactions table
        const transactionsNulls = await client.query(`
            SELECT COUNT(*) as count FROM transactions WHERE amount IS NULL OR original_amount IS NULL
        `);
        console.log(`📊 Transactions with NULL amounts: ${transactionsNulls.rows[0].count}`);

        // Check goals table
        const goalsNulls = await client.query(`
            SELECT COUNT(*) as count FROM goals WHERE target_amount IS NULL OR current_amount IS NULL
        `);
        console.log(`📊 Goals with NULL amounts: ${goalsNulls.rows[0].count}`);

        // Check users table
        const usersNulls = await client.query(`
            SELECT COUNT(*) as count FROM users WHERE initial_balance IS NULL
        `);
        console.log(`📊 Users with NULL initial_balance: ${usersNulls.rows[0].count}`);

        // Fix NULL values
        console.log('\n🔧 Fixing NULL values...');

        // Fix budgets
        await client.query(`UPDATE budgets SET amount = 0 WHERE amount IS NULL`);
        await client.query(`UPDATE budgets SET amount_spent = 0 WHERE amount_spent IS NULL`);
        console.log('✅ Fixed budgets table');

        // Fix transactions
        await client.query(`UPDATE transactions SET amount = 0 WHERE amount IS NULL`);
        await client.query(`UPDATE transactions SET original_amount = 0 WHERE original_amount IS NULL`);
        console.log('✅ Fixed transactions table');

        // Fix goals
        await client.query(`UPDATE goals SET target_amount = 0 WHERE target_amount IS NULL`);
        await client.query(`UPDATE goals SET current_amount = 0 WHERE current_amount IS NULL`);
        console.log('✅ Fixed goals table');

        // Fix users
        await client.query(`UPDATE users SET initial_balance = 0 WHERE initial_balance IS NULL`);
        console.log('✅ Fixed users table');

        // Verify no more NULLs
        console.log('\n✅ Verification after fix:');
        const budgetsCheck = await client.query(`SELECT COUNT(*) as count FROM budgets WHERE amount IS NULL OR amount_spent IS NULL`);
        console.log(`📊 Budgets with NULL amounts: ${budgetsCheck.rows[0].count}`);

        const transactionsCheck = await client.query(`SELECT COUNT(*) as count FROM transactions WHERE amount IS NULL OR original_amount IS NULL`);
        console.log(`📊 Transactions with NULL amounts: ${transactionsCheck.rows[0].count}`);

        const goalsCheck = await client.query(`SELECT COUNT(*) as count FROM goals WHERE target_amount IS NULL OR current_amount IS NULL`);
        console.log(`📊 Goals with NULL amounts: ${goalsCheck.rows[0].count}`);

        const usersCheck = await client.query(`SELECT COUNT(*) as count FROM users WHERE initial_balance IS NULL`);
        console.log(`📊 Users with NULL initial_balance: ${usersCheck.rows[0].count}`);

        console.log('\n🎉 All NULL values have been fixed!');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    } finally {
        await client.end();
        console.log('🔌 Database connection closed');
    }
}

checkAndFixNulls();
