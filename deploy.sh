#!/bin/bash

# Finance App Docker Deployment Script
echo "🚀 Starting Finance App Docker Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create logs directory if it doesn't exist
print_status "Creating logs directory..."
mkdir -p logs

# Stop and remove existing containers
print_status "Stopping existing containers..."
docker-compose down

# Remove old images (optional - uncomment if you want to rebuild from scratch)
print_warning "Removing old images..."
docker rmi $(docker images -q finance_app_be_finance-app) 2>/dev/null || true

# Build and start containers
print_status "Building and starting containers..."
docker-compose up -d --build

# Wait for containers to start
print_status "Waiting for containers to start..."
sleep 10

# Show container status
print_status "Container status:"
docker-compose ps

# Check if application is healthy
print_status "Checking application health..."
sleep 5
curl -f https://vuquangduy.online/api/v1/debug/health || print_warning "Health check failed - application might still be starting"

print_status "✅ Deployment completed successfully!"
print_status "🌐 Application is running on: https://vuquangduy.online"
print_status "📊 Health check: https://vuquangduy.online/api/v1/debug/health"
print_status "📋 View logs with: docker-compose logs -f finance-app"
print_status "🔄 Restart with: docker-compose restart finance-app"
print_status "🛑 Stop with: docker-compose down"

# Show logs
print_status "Showing recent logs (press Ctrl+C to exit):"
docker-compose logs --tail=50 -f finance-app
