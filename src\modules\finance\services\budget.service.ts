import { Injectable, BadRequestException, NotFoundException, ConflictException, Inject, Scope } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { REQUEST } from '@nestjs/core';
import { Request, Response } from 'express';
import * as PDFDocument from 'pdfkit';
import { Budget } from '../entity/budget.entity';
import { Transaction } from '../entity/transaction.entity';
import { Category } from '../entity/category.entity';
import { User } from '../../users/entity';
import { IUser } from '../../users/interfaces';
import {
    CreateBudgetDto,
    UpdateBudgetDto,
    BudgetQueryDto,
    BudgetOverviewDto,
    BudgetReportDto,
    BudgetCategoryReportDto,
    TransactionSummaryDto,
    ExportBudgetReportDto
} from '../dto/budget.dto';
import { BudgetStatus } from '../enums';
import { CategoryType, TransactionType } from '../enums';

@Injectable({ scope: Scope.REQUEST })
export class BudgetService {
    constructor(
        @InjectRepository(Budget)
        private readonly budgetRepository: Repository<Budget>,
        @InjectRepository(Transaction)
        private readonly transactionRepository: Repository<Transaction>,
        @InjectRepository(Category)
        private readonly categoryRepository: Repository<Category>,
        @InjectRepository(User)
        private readonly userRepository: Repository<User>,
        @Inject(REQUEST) private request: Request,
    ) {}

    private get currentUser(): IUser {
        return this.request.user as IUser;
    }

    private get userId(): string {
        if (!this.currentUser?.userId) {
            throw new BadRequestException('User not authenticated');
        }
        return this.currentUser.userId;
    }

    async createBudget(createBudgetDto: CreateBudgetDto): Promise<Budget> {
        const { categoryId, month, year, amount } = createBudgetDto;

        // Validate category exists and belongs to user
        const category = await this.categoryRepository.findOne({
            where: {
                id: categoryId,
                user_id: this.userId,
                type: CategoryType.EXPENSE // Only expense categories can have budgets
            }
        });
        if (!category) {
            throw new NotFoundException('Category not found or not an expense category');
        }

        // Check if budget already exists for this category, month, year
        const existingBudget = await this.budgetRepository.findOne({
            where: {
                user_id: this.userId,
                category_id: categoryId,
                month,
                year,
                is_active: true
            }
        });

        if (existingBudget) {
            throw new ConflictException('Budget already exists for this category and period');
        }

        // Validate total budget doesn't exceed user's financial capacity
        await this.validateTotalBudget(month, year, amount);

        // Create budget with initial amount_spent as 0
        const budget = this.budgetRepository.create({
            user_id: this.userId,
            category_id: categoryId,
            month,
            year,
            amount,
            amount_spent: 0,
            status: BudgetStatus.ACTIVE,
            is_active: true
        });

        return await this.budgetRepository.save(budget);
    }

    async updateBudget(budgetId: string, updateBudgetDto: UpdateBudgetDto): Promise<Budget> {
        const { amount, status } = updateBudgetDto;

        const budget = await this.budgetRepository.findOne({
            where: { id: budgetId, user_id: this.userId, is_active: true }
        });

        if (!budget) {
            throw new NotFoundException('Budget not found');
        }

        if (amount !== undefined) {
            // Validate new total budget
            const currentAmount = budget.amount;
            const difference = amount - currentAmount;

            if (difference > 0) {
                await this.validateTotalBudget(budget.month, budget.year, difference);
            }

            budget.amount = amount;
        }

        if (status !== undefined) {
            budget.status = status;
        }

        return await this.budgetRepository.save(budget);
    }

    async deleteBudget(budgetId: string): Promise<void> {
        const budget = await this.budgetRepository.findOne({
            where: { id: budgetId, user_id: this.userId, is_active: true }
        });

        if (!budget) {
            throw new NotFoundException('Budget not found');
        }

        budget.is_active = false;
        await this.budgetRepository.save(budget);
    }

    async getBudgets(query: BudgetQueryDto): Promise<Budget[]> {
        const { month, year, categoryId, status } = query;

        const whereConditions: any = {
            user_id: this.userId,
            is_active: true
        };

        if (month) whereConditions.month = month;
        if (year) whereConditions.year = year;
        if (categoryId) whereConditions.category_id = categoryId;
        if (status) whereConditions.status = status;

        return await this.budgetRepository.find({
            where: whereConditions,
            relations: ['category'],
            order: { year: 'DESC', month: 'DESC' }
        });
    }

    async getBudgetById(budgetId: string): Promise<Budget> {
        const budget = await this.budgetRepository.findOne({
            where: { id: budgetId, user_id: this.userId, is_active: true },
            relations: ['category']
        });

        if (!budget) {
            throw new NotFoundException('Budget not found');
        }

        return budget;
    }

    async getBudgetOverview(month: number, year: number): Promise<BudgetOverviewDto[]> {
        const whereConditions = {
            user_id: this.userId,
            month: parseInt(month.toString()),
            year: parseInt(year.toString()),
            is_active: true,
            status: BudgetStatus.ACTIVE
        };

        const budgets = await this.budgetRepository.find({
            where: whereConditions,
            relations: ['category']
        });

        return budgets.map(budget => {
            const remainingAmount = budget.amount - budget.amount_spent;
            const progressPercent = budget.amount > 0 ? Math.round((budget.amount_spent / budget.amount) * 100) : 0;
            const warning = progressPercent >= 80;

            return {
                categoryId: budget.category_id,
                categoryName: budget.category?.name || 'Unknown Category',
                budgetAmount: budget.amount,
                spentAmount: budget.amount_spent,
                remainingAmount,
                progressPercent,
                warning
            };
        });
    }

    private async validateTotalBudget(month: number, year: number, additionalAmount: number): Promise<void> {
        // Get current total budget for the month
        const currentBudgets = await this.budgetRepository.find({
            where: {
                user_id: this.userId,
                month,
                year,
                is_active: true
            }
        });

        const currentTotalBudget = currentBudgets.reduce((sum, budget) => sum + budget.amount, 0);
        const newTotalBudget = currentTotalBudget + additionalAmount;

        // Get user's financial capacity
        const user = await this.userRepository.findOne({
            where: { id: this.userId }
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        // Validation: total budget shouldn't exceed user's initial balance * 2
        const maxAllowedBudget = (user.initial_balance || 0) * 2;

        if (newTotalBudget > maxAllowedBudget) {
            throw new BadRequestException(
                `Total budget (${newTotalBudget}) exceeds your financial capacity (${maxAllowedBudget}). Please adjust your budget amounts.`
            );
        }
    }

    async getBudgetReport(month: number, year: number): Promise<BudgetReportDto> {
        const budgets = await this.budgetRepository.find({
            where: {
                user_id: this.userId,
                month,
                year,
                is_active: true
            },
            relations: ['category']
        });

        let totalBudget = 0;
        let totalSpent = 0;
        const categories: BudgetCategoryReportDto[] = [];

        for (const budget of budgets) {
            const transactions = await this.getTransactions(budget.category_id, month, year);

            totalBudget += budget.amount;
            totalSpent += budget.amount_spent;

            categories.push({
                categoryId: budget.category_id,
                categoryName: budget.category?.name || 'Unknown Category',
                budgetAmount: budget.amount,
                spentAmount: budget.amount_spent,
                remainingAmount: budget.amount - budget.amount_spent,
                transactions
            });
        }

        return {
            totalBudget,
            totalSpent,
            totalRemaining: totalBudget - totalSpent,
            categories,
            generatedAt: new Date()
        };
    }

    private async getTransactions(categoryId: string, month: number, year: number): Promise<TransactionSummaryDto[]> {
        const startDate = new Date(year, month - 1, 1);
        const endDate = new Date(year, month, 0, 23, 59, 59);

        const transactions = await this.transactionRepository.find({
            where: {
                user_id: this.userId,
                category_id: categoryId,
                type: TransactionType.EXPENSE,
                transaction_date: Between(startDate, endDate)
            },
            order: { transaction_date: 'DESC' }
        });

        return transactions.map(transaction => ({
            id: transaction.id,
            amount: transaction.amount,
            transaction_date: transaction.transaction_date,
            description: transaction.description || ''
        }));
    }

    async exportBudgetReport(exportDto: ExportBudgetReportDto, res: Response): Promise<void> {
        const { month, year, format } = exportDto;
        const report = await this.getBudgetReport(month, year);

        if (format === 'csv') {
            await this.exportToCSV(report, month, year, res);
        } else {
            await this.exportToPDF(report, month, year, res);
        }
    }

    private async exportToCSV(report: BudgetReportDto, month: number, year: number, res: Response): Promise<void> {
        const filename = `budget-report-${year}-${month.toString().padStart(2, '0')}.csv`;

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

        // CSV Header
        let csvContent = 'Category,Budget Amount,Spent Amount,Remaining Amount,Progress %\n';

        // Add summary row
        const totalProgress = report.totalBudget > 0 ? Math.round((report.totalSpent / report.totalBudget) * 100) : 0;
        csvContent += `TOTAL,${report.totalBudget},${report.totalSpent},${report.totalRemaining},${totalProgress}%\n\n`;

        // Add category details
        for (const category of report.categories) {
            const progress = category.budgetAmount > 0 ? Math.round((category.spentAmount / category.budgetAmount) * 100) : 0;
            csvContent += `"${category.categoryName}",${category.budgetAmount},${category.spentAmount},${category.remainingAmount},${progress}%\n`;
        }

        res.send(csvContent);
    }

    private async exportToPDF(report: BudgetReportDto, month: number, year: number, res: Response): Promise<void> {
        const filename = `budget-report-${year}-${month.toString().padStart(2, '0')}.pdf`;

        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

        const doc = new PDFDocument();
        doc.pipe(res);

        // Title
        doc.fontSize(20).text(`Budget Report - ${month}/${year}`, 50, 50);
        doc.fontSize(12).text(`Generated on: ${report.generatedAt.toLocaleDateString()}`, 50, 80);

        // Summary
        doc.fontSize(16).text('Summary', 50, 120);
        doc.fontSize(12)
           .text(`Total Budget: ${report.totalBudget.toLocaleString()}`, 50, 150)
           .text(`Total Spent: ${report.totalSpent.toLocaleString()}`, 50, 170)
           .text(`Total Remaining: ${report.totalRemaining.toLocaleString()}`, 50, 190);

        // Categories
        let yPosition = 230;
        doc.fontSize(16).text('Categories Breakdown', 50, yPosition);
        yPosition += 30;

        for (const category of report.categories) {
            if (yPosition > 700) {
                doc.addPage();
                yPosition = 50;
            }

            const progress = category.budgetAmount > 0 ? Math.round((category.spentAmount / category.budgetAmount) * 100) : 0;

            doc.fontSize(14).text(category.categoryName, 50, yPosition);
            doc.fontSize(10)
               .text(`Budget: ${category.budgetAmount.toLocaleString()}`, 50, yPosition + 20)
               .text(`Spent: ${category.spentAmount.toLocaleString()} (${progress}%)`, 200, yPosition + 20)
               .text(`Remaining: ${category.remainingAmount.toLocaleString()}`, 350, yPosition + 20);

            yPosition += 50;
        }

        doc.end();
    }
}
