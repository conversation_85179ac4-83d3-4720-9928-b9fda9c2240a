import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { User } from "./entity";
import { UserController } from "./controllers";
import { AdminUserService, UserService } from "./services";

@Module({
    imports: [TypeOrmModule.forFeature([User])],
    controllers: [UserController],
    providers: [UserService, AdminUserService],
    exports: [UserService]
})

export class UserModule { }