import { BadRequestException, Injectable, UnauthorizedException, Logger, InternalServerErrorException, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { LoginDto, RegisterDto, ResetPasswordDto, VerifyOtpDto } from "../dto";
import { User } from "@modules/users/entity";
import { Repository } from "typeorm";
import { IUser } from "@modules/users/interfaces";
import { JwtService } from "@nestjs/jwt";
import { config } from "@config";
import * as bcrypt from "bcrypt";
import * as _ from "lodash";
import { RefreshTokenDto } from "../dto/refresh-token.dto";
import { UserStatus, UserProvide, UserRole } from "@modules/users/enums";
import { } from 'passport-google-oauth20';
import { MailService } from "@provider/mail/mail.service";
import { ForgotPasswordDto } from "../dto/forgot-password.dto";
import { Otp } from "@base/otp/otp.entity";
import { OtpService } from "@base/otp/otp.service";

@Injectable()
export class AuthService {
    private readonly logger = new Logger(AuthService.name);

    constructor(
        @InjectRepository(User) private readonly userRepository: Repository<User>,
        @InjectRepository(Otp) private readonly otpRepository: Repository<Otp>,

        private readonly jwtService: JwtService,
        private readonly mailService: MailService,
        private readonly otpService: OtpService
    ) { }

    private async generatePairToken(payload: IUser) {
        const accessToken = this.jwtService.sign(payload, { expiresIn: config.AC_TOKEN_EXPIRED });
        const refreshToken = this.jwtService.sign(payload, { expiresIn: config.RF_TOKEN_EXPIRED });
        return { accessToken, refreshToken };

    }

    async register(body: RegisterDto) {
        const { username, password, email, firstName, lastName, gender, dateOfBirth, phone } = body;
        const user = await this.userRepository.findOne({ where: [{ username }, { email }] });
        if (user) throw new BadRequestException("User or Email already exists");
        const hashPassword = await bcrypt.hash(password, 10);

        const newUser = this.userRepository.create({
            ...body,
            password: hashPassword,
        })
        const savedUser = await this.userRepository.save(newUser);

        const payload: IUser = {
            userId: savedUser.id,
            username: savedUser.username,
            email: savedUser.email,
            roles: [savedUser.role],
        }

        const { accessToken, refreshToken } = await this.generatePairToken(payload);
        const hashRefreshToken = await bcrypt.hash(refreshToken, 10);

        await this.userRepository.update(savedUser.id, { refreshToken: hashRefreshToken });

        return {
            data: {
                user: _.pick(savedUser, ['username', 'email', 'firstName', 'lastName', 'gender', 'dateOfBirth', 'phone']),
                accessToken,
                refreshToken

            }
        }


    }

    async login(body: LoginDto) {
        const { email, password } = body;
        const user = await this.userRepository.findOne({ where: { email: email, status: UserStatus.ACTIVE } });

        if (!user) throw new BadRequestException("Invalid username/email or password");

        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) {
            throw new BadRequestException("Invalid username/email or password");
        }

        const payload: IUser = {
            userId: user.id,
            username: user.username,
            email: user.email,
            roles: [user.role],
        };

        const { accessToken, refreshToken } = await this.generatePairToken(payload);
        const hashRefreshToken = await bcrypt.hash(refreshToken, 10);

        await this.userRepository.update(user.id, { refreshToken: hashRefreshToken });

        return {
            data: {
                user: _.pick(user, ['username', 'email', 'firstName', 'lastName', 'gender', 'dateOfBirth', 'phone']),
                accessToken,
                refreshToken
            }
        };
    }


    async refreshToken(body: RefreshTokenDto) {

        const { refreshToken } = body;
        const payload = await this.jwtService.verifyAsync<IUser>(refreshToken);

        const user = await this.userRepository.findOne({
            where: { id: payload.userId },
            select: ['id', 'username', 'email', 'role', 'refreshToken'],
        });

        if (!user) throw new UnauthorizedException('User not found');

        const isMatch = await bcrypt.compare(refreshToken, user.refreshToken);
        if (!isMatch) throw new UnauthorizedException('Invalid refresh token');

        const newPayload: IUser = {
            userId: user.id,
            username: user.username,
            email: user.email,
            roles: [user.role],
        };

        const { accessToken, refreshToken: newRefreshToken } = await this.generatePairToken(newPayload);
        const hashRefreshToken = await bcrypt.hash(newRefreshToken, 10);
        await this.userRepository.update(user.id, { refreshToken: hashRefreshToken });

        return {
            data: {
                accessToken,
                refreshToken: newRefreshToken,
            },
        };

    }
    async googleLogin(req: any): Promise<{ data: any }> {
        const startTime = Date.now();
        const requestId = this.generateRequestId();

        this.logger.log(`🔄 [${requestId}] Processing Google OAuth login`);

        try {
            // Validate request
            if (!req || !req.user) {
                this.logger.error(`❌ [${requestId}] No user data from Google OAuth`);
                throw new BadRequestException('No user from Google OAuth');
            }

            const user = req.user;
            const securityContext = this.extractSecurityContext(req);

            this.logger.debug(`👤 [${requestId}] Processing user: ${user.email}`);

            // Validate user data
            this.validateUserData(user);

            // Create JWT payload
            const payload: IUser = {
                userId: user.id,
                username: user.username,
                email: user.email,
                roles: [user.role],
            };

            // Generate tokens
            const { accessToken, refreshToken } = await this.generatePairToken(payload);
            this.logger.debug(`🔑 [${requestId}] Tokens generated successfully`);

            // Hash and save refresh token
            const hashRefreshToken = await bcrypt.hash(refreshToken, 10);
            await this.userRepository.update(user.id, {
                refreshToken: hashRefreshToken,
            });

            this.logger.log(`✅ [${requestId}] Google OAuth login successful for user: ${user.email} (${Date.now() - startTime}ms)`);

            // Prepare response data
            const responseData = {
                user: _.pick(user, [
                    'id',
                    'username',
                    'email',
                    'firstName',
                    'lastName',
                    'avatar',
                    'provide',
                    'status',
                    'role'
                ]),
                accessToken,
                refreshToken,
                expiresIn: this.getTokenExpirationTime(config.AC_TOKEN_EXPIRED),
                tokenType: 'Bearer',
                provider: 'google',
                loginTime: new Date().toISOString(),
            };

            // Log successful authentication
            this.logOAuthEvent('oauth_success', user, securityContext, requestId);

            return { data: responseData };

        } catch (error) {
            this.logger.error(`❌ [${requestId}] Google OAuth login error:`, error);
            this.logOAuthEvent('oauth_error', null, this.extractSecurityContext(req), requestId, error.message);

            if (error instanceof BadRequestException) {
                throw error;
            }

            throw new InternalServerErrorException('Google OAuth login failed');
        }
    }

    async forgotPassword(body: ForgotPasswordDto): Promise<boolean> {
        const { email } = body;

        const user = await this.userRepository.findOne({
            where: { email, status: UserStatus.ACTIVE },
        });

        if (!user) {
            throw new BadRequestException('Email does not exist in the system');
        }

        const { otp, expiresAt } = this.otpService.generateOtp()

        await this.otpRepository.delete({ email });
        await this.otpRepository.save({ email, otp, expiresAt });

        await this.mailService.sendMailForgotPassword(user, parseInt(otp), email);

        return true;
    }

    async verifyOtp(body: VerifyOtpDto): Promise<boolean> {
        const { email, otp } = body;

        const otpEntity = await this.otpRepository.findOne({ where: { email, otp } });

        if (!otpEntity) {
            throw new BadRequestException('Incorrect OTP code');
        }

        if (otpEntity.expiresAt.getTime() < Date.now()) {
            await this.otpRepository.delete({ email });
            throw new BadRequestException('OTP has expired');
        }

        return true;
    }

    async resetPassword(body: ResetPasswordDto): Promise<boolean> {
        const { email, otp, newPassword } = body;

        await this.verifyOtp({ email, otp });

        const user = await this.userRepository.findOne({
            where: { email, status: UserStatus.ACTIVE },
        });

        if (!user) {
            throw new BadRequestException('User does not exist or is inactive');
        }

        const isSamePassword = await bcrypt.compare(newPassword, user.password);
        if (isSamePassword) {
            throw new BadRequestException('New password must be different from the current password');
        }

        const hashedPassword = await bcrypt.hash(newPassword, 10);
        await this.userRepository.update(user.id, { password: hashedPassword });

        await this.otpRepository.delete({ email });

        return true;
    }

    // Helper methods for Google OAuth
    private validateUserData(user: any): void {
        const requiredFields = ['id', 'email', 'username'];
        const missingFields = requiredFields.filter(field => !user[field]);

        if (missingFields.length > 0) {
            throw new BadRequestException(`Missing required user fields: ${missingFields.join(', ')}`);
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(user.email)) {
            throw new BadRequestException('Invalid email format');
        }
    }

    private extractSecurityContext(req: any): any {
        return {
            ipAddress: req?.ip || req?.connection?.remoteAddress || 'unknown',
            userAgent: req?.headers?.['user-agent'] || 'unknown',
            timestamp: new Date(),
            sessionId: req?.sessionID,
        };
    }

    private generateRequestId(): string {
        return `oauth_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    private getTokenExpirationTime(expiresIn: string): number {
        // Convert string like "15m", "1h", "7d" to seconds
        const timeMap = { m: 60, h: 3600, d: 86400 };
        const match = expiresIn.match(/^(\d+)([mhd])$/);

        if (!match) return 900; // Default 15 minutes

        const [, value, unit] = match;
        return parseInt(value) * timeMap[unit as keyof typeof timeMap];
    }

    private logOAuthEvent(
        action: string,
        user: any,
        securityContext: any,
        requestId: string,
        error?: string
    ): void {
        const logData = {
            requestId,
            action,
            provider: 'google',
            userId: user?.id,
            email: user?.email,
            ipAddress: securityContext?.ipAddress,
            userAgent: securityContext?.userAgent,
            timestamp: new Date(),
            error,
        };

        if (error) {
            this.logger.error(`🚨 OAuth Event [${requestId}]:`, logData);
        } else {
            this.logger.log(`📊 OAuth Event [${requestId}]:`, logData);
        }

        // Here you could save to audit log table if needed
        // await this.auditLogService.create(logData);
    }
}
