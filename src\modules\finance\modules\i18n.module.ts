import { Module } from '@nestjs/common';
import { I18nModule as NestI18nModule, AcceptLanguageResolver, QueryResolver, HeaderResolver } from 'nestjs-i18n';
import { join } from 'path';
import { I18nService } from '../services/i18n.service';

@Module({
    imports: [
        NestI18nModule.forRoot({
            fallbackLanguage: 'en',
            loaderOptions: {
                path: join(process.cwd(), 'src/i18n/'),
                watch: true,
            },
            resolvers: [
                { use: QueryResolver, options: ['lang'] },
                AcceptLanguageResolver,
                new HeaderResolver(['x-lang']),
            ],
        }),
    ],
    providers: [I18nService],
    exports: [I18nService, NestI18nModule],
})
export class I18nModule { }
