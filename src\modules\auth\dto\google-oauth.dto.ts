import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Url, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class GoogleProfileDto {
  @ApiProperty({ description: 'Google user ID' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ description: 'User email from Google' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'User first name' })
  @IsString()
  @IsOptional()
  firstName?: string;

  @ApiProperty({ description: 'User last name' })
  @IsString()
  @IsOptional()
  lastName?: string;

  @ApiProperty({ description: 'User display name' })
  @IsString()
  @IsOptional()
  displayName?: string;

  @ApiProperty({ description: 'User avatar URL' })
  @IsUrl()
  @IsOptional()
  avatar?: string;

  @ApiProperty({ description: 'Email verified status' })
  @IsOptional()
  emailVerified?: boolean;
}

export class GoogleOAuthCallbackDto {
  @ApiProperty({ description: 'Authorization code from Google' })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({ description: 'State parameter for CSRF protection' })
  @IsString()
  @IsOptional()
  state?: string;

  @ApiProperty({ description: 'Scope granted by user' })
  @IsString()
  @IsOptional()
  scope?: string;

  @ApiProperty({ description: 'Auth user parameter' })
  @IsString()
  @IsOptional()
  authuser?: string;

  @ApiProperty({ description: 'Prompt parameter' })
  @IsString()
  @IsOptional()
  prompt?: string;
}

export class GoogleOAuthResponseDto {
  @ApiProperty({ description: 'Access token for API calls' })
  @IsString()
  @IsNotEmpty()
  accessToken: string;

  @ApiProperty({ description: 'Refresh token for token renewal' })
  @IsString()
  @IsNotEmpty()
  refreshToken: string;

  @ApiProperty({ description: 'User information' })
  user: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    avatar?: string;
    provider: string;
  };

  @ApiProperty({ description: 'Token expiration time' })
  expiresIn: number;
}

export class DeepLinkRedirectDto {
  @ApiProperty({ description: 'Mobile app deep link URL' })
  @IsUrl()
  @IsNotEmpty()
  deepLinkUrl: string;

  @ApiProperty({ description: 'Access token parameter' })
  @IsString()
  @IsNotEmpty()
  accessToken: string;

  @ApiProperty({ description: 'Refresh token parameter' })
  @IsString()
  @IsNotEmpty()
  refreshToken: string;

  @ApiProperty({ description: 'User email parameter' })
  @IsEmail()
  @IsOptional()
  userEmail?: string;

  @ApiProperty({ description: 'Error code if any' })
  @IsString()
  @IsOptional()
  error?: string;

  @ApiProperty({ description: 'Error message if any' })
  @IsString()
  @IsOptional()
  message?: string;
}
