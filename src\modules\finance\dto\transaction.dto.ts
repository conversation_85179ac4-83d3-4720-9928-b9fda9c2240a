import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
    IsString,
    IsNumber,
    IsDate,
    IsOptional,
    IsEnum,
    IsUUID,
    Min,
    IsInt,
    MaxDate,
    IsNotEmpty,
    Max<PERSON>ength,
    MinDate,
    <PERSON>In,
    Max
} from 'class-validator';
import { TransactionType, InputMethod, Currency } from '../enums';

export class CreateTransactionDto {
    @ApiProperty({
        description: 'Amount in the smallest currency unit (e.g., cents)',
        example: 1000000,
        minimum: 1
    })
    @Transform(({ value }) => parseInt(value))
    @Type(() => Number)
    @IsNumber({ maxDecimalPlaces: 0 }, { message: 'Amount must be a whole number' })
    @Min(1, { message: 'Amount must be greater than 0' })
    amount: number;

    @ApiProperty({
        description: 'Type of transaction',
        enum: TransactionType,
        example: TransactionType.EXPENSE
    })
    @IsEnum(TransactionType, { message: 'Type must be either income or expense' })
    @IsNotEmpty({ message: 'Transaction type is required' })
    type: TransactionType;

    @ApiPropertyOptional({
        description: 'Category ID (optional)',
        example: 'uuid-string'
    })
    @IsOptional()
    @IsUUID(4, { message: 'Category ID must be a valid UUID' })
    category_id?: string;

    @ApiProperty({
        description: 'Transaction date (cannot be in the future)',
        example: '2024-03-20T00:00:00.000Z'
    })
    @Transform(({ value }) => {
        if (typeof value === 'string') {
            return new Date(value);
        }
        return value;
    })
    @Type(() => Date)
    @IsDate({ message: 'Transaction date must be a valid date' })
    @MaxDate(new Date(), { message: 'Transaction date cannot be in the future' })
    @MinDate(new Date('2000-01-01'), { message: 'Transaction date cannot be before year 2000' })
    transaction_date: Date;

    @ApiPropertyOptional({
        description: 'Transaction description',
        example: 'Grocery shopping',
        maxLength: 255
    })
    @IsOptional()
    @IsString({ message: 'Description must be a string' })
    @MaxLength(255, { message: 'Description cannot exceed 255 characters' })
    description?: string;

    @ApiPropertyOptional({
        description: 'Additional notes',
        example: 'Monthly groceries from Walmart',
        maxLength: 1000
    })
    @IsOptional()
    @IsString({ message: 'Notes must be a string' })
    @MaxLength(1000, { message: 'Notes cannot exceed 1000 characters' })
    notes?: string;

    @ApiPropertyOptional({
        description: 'Input method',
        enum: InputMethod,
        default: InputMethod.MANUAL
    })
    @IsOptional()
    @IsEnum(InputMethod, { message: 'Input method must be one of: manual, voice, chat, image' })
    input_method?: InputMethod = InputMethod.MANUAL;

    @ApiPropertyOptional({
        description: 'Image URL for transaction receipt or proof',
        example: 'https://example.com/receipt.jpg'
    })
    @IsOptional()
    @IsString({ message: 'Image URL must be a string' })
    image_url?: string;

    @ApiPropertyOptional({
        description: 'Original currency code',
        enum: Currency,
        example: Currency.VND,
        default: Currency.USD
    })
    @IsOptional()
    @IsEnum(Currency, { message: 'Currency must be one of: USD, VND, CNY' })
    original_currency?: Currency = Currency.USD;
}

export class UpdateTransactionDto {
    @ApiPropertyOptional({
        description: 'Amount in the smallest currency unit (e.g., cents)',
        example: 1000000,
        minimum: 1
    })
    @IsOptional()
    @Transform(({ value }) => value ? parseInt(value) : value)
    @Type(() => Number)
    @IsNumber({ maxDecimalPlaces: 0 }, { message: 'Amount must be a whole number' })
    @Min(1, { message: 'Amount must be greater than 0' })
    amount?: number;

    @ApiPropertyOptional({
        description: 'Type of transaction',
        enum: TransactionType,
        example: TransactionType.EXPENSE
    })
    @IsOptional()
    @IsEnum(TransactionType, { message: 'Type must be either income or expense' })
    type?: TransactionType;

    @ApiPropertyOptional({
        description: 'Category ID',
        example: 'uuid-string'
    })
    @IsOptional()
    @IsUUID(4, { message: 'Category ID must be a valid UUID' })
    category_id?: string;

    @ApiPropertyOptional({
        description: 'Transaction date (cannot be in the future)',
        example: '2024-03-20T00:00:00.000Z'
    })
    @IsOptional()
    @Transform(({ value }) => {
        if (typeof value === 'string') {
            return new Date(value);
        }
        return value;
    })
    @Type(() => Date)
    @IsDate({ message: 'Transaction date must be a valid date' })
    @MaxDate(new Date(), { message: 'Transaction date cannot be in the future' })
    @MinDate(new Date('2000-01-01'), { message: 'Transaction date cannot be before year 2000' })
    transaction_date?: Date;

    @ApiPropertyOptional({
        description: 'Transaction description',
        example: 'Grocery shopping',
        maxLength: 255
    })
    @IsOptional()
    @IsString({ message: 'Description must be a string' })
    @MaxLength(255, { message: 'Description cannot exceed 255 characters' })
    description?: string;

    @ApiPropertyOptional({
        description: 'Additional notes',
        example: 'Monthly groceries from Walmart',
        maxLength: 1000
    })
    @IsOptional()
    @IsString({ message: 'Notes must be a string' })
    @MaxLength(1000, { message: 'Notes cannot exceed 1000 characters' })
    notes?: string;

    @ApiPropertyOptional({
        description: 'Input method',
        enum: InputMethod,
        default: InputMethod.MANUAL
    })
    @IsOptional()
    @IsEnum(InputMethod, { message: 'Input method must be one of: manual, voice, chat, image' })
    input_method?: InputMethod;

    @ApiPropertyOptional({
        description: 'Image URL for transaction receipt or proof',
        example: 'https://example.com/receipt.jpg'
    })
    @IsOptional()
    @IsString({ message: 'Image URL must be a string' })
    image_url?: string;

    @ApiPropertyOptional({
        description: 'Original currency code',
        enum: Currency,
        example: Currency.VND
    })
    @IsOptional()
    @IsEnum(Currency, { message: 'Currency must be one of: USD, VND, CNY' })
    original_currency?: Currency;
}

export class TransactionQueryDto {
    @ApiPropertyOptional({
        description: 'Page number',
        example: 1,
        minimum: 1,
        default: 1
    })
    @IsOptional()
    @Transform(({ value }) => parseInt(value) || 1)
    @Type(() => Number)
    @IsInt({ message: 'Page must be an integer' })
    @Min(1, { message: 'Page must be at least 1' })
    page?: number = 1;

    @ApiPropertyOptional({
        description: 'Items per page (max 100)',
        example: 10,
        minimum: 1,
        maximum: 100,
        default: 10
    })
    @IsOptional()
    @Transform(({ value }) => parseInt(value) || 10)
    @Type(() => Number)
    @IsInt({ message: 'Limit must be an integer' })
    @Min(1, { message: 'Limit must be at least 1' })
    @Max(100, { message: 'Limit cannot exceed 100' })
    limit?: number = 10;

    @ApiPropertyOptional({
        description: 'Start date for filtering',
        example: '2024-03-01T00:00:00.000Z'
    })
    @IsOptional()
    @Transform(({ value }) => {
        if (typeof value === 'string') {
            return new Date(value);
        }
        return value;
    })
    @Type(() => Date)
    @IsDate({ message: 'Start date must be a valid date' })
    start_date?: Date;

    @ApiPropertyOptional({
        description: 'End date for filtering',
        example: '2024-03-31T23:59:59.999Z'
    })
    @IsOptional()
    @Transform(({ value }) => {
        if (typeof value === 'string') {
            return new Date(value);
        }
        return value;
    })
    @Type(() => Date)
    @IsDate({ message: 'End date must be a valid date' })
    end_date?: Date;

    @ApiPropertyOptional({
        description: 'Filter by transaction type',
        enum: TransactionType
    })
    @IsOptional()
    @IsEnum(TransactionType, { message: 'Type must be either income or expense' })
    type?: TransactionType;

    @ApiPropertyOptional({
        description: 'Filter by category',
        example: 'uuid-string'
    })
    @IsOptional()
    @IsUUID(4, { message: 'Category ID must be a valid UUID' })
    category_id?: string;

    @ApiPropertyOptional({
        description: 'Search in description and notes',
        example: 'grocery',
        maxLength: 100
    })
    @IsOptional()
    @IsString({ message: 'Search must be a string' })
    @MaxLength(100, { message: 'Search cannot exceed 100 characters' })
    search?: string;

    @ApiPropertyOptional({
        description: 'Sort by field',
        enum: ['amount', 'transaction_date', 'created_at'],
        default: 'transaction_date'
    })
    @IsOptional()
    @IsIn(['amount', 'transaction_date', 'created_at'], {
        message: 'Sort by must be one of: amount, transaction_date, created_at'
    })
    sort_by?: 'amount' | 'transaction_date' | 'created_at' = 'transaction_date';

    @ApiPropertyOptional({
        description: 'Sort order',
        enum: ['ASC', 'DESC'],
        default: 'DESC'
    })
    @IsOptional()
    @IsIn(['ASC', 'DESC'], { message: 'Sort order must be either ASC or DESC' })
    sort_order?: 'ASC' | 'DESC' = 'DESC';
}

export class CategoryStatisticsDto {
    @ApiProperty({
        description: 'Category ID',
        example: 'uuid-string'
    })
    category_id: string;

    @ApiProperty({
        description: 'Category name',
        example: 'Food & Dining'
    })
    category_name: string;

    @ApiProperty({
        description: 'Total amount for this category',
        example: 1500000
    })
    total_amount: number;

    @ApiProperty({
        description: 'Transaction count for this category',
        example: 8
    })
    transaction_count: number;

    @ApiProperty({
        description: 'Percentage of total',
        example: 25.5
    })
    percentage: number;
}

export class TransactionStatisticsDto {
    @ApiProperty({
        description: 'Total income amount',
        example: 5000000
    })
    total_income: number;

    @ApiProperty({
        description: 'Total expense amount',
        example: 3000000
    })
    total_expense: number;

    @ApiProperty({
        description: 'Net amount (income - expense)',
        example: 2000000
    })
    net_amount: number;

    @ApiProperty({
        description: 'Transaction count',
        example: 25
    })
    transaction_count: number;

    @ApiProperty({
        description: 'Average transaction amount',
        example: 160000
    })
    average_amount: number;

    @ApiProperty({
        description: 'Statistics by category',
        type: [CategoryStatisticsDto]
    })
    categories: CategoryStatisticsDto[];
}

export class DateRangeQueryDto {
    @ApiProperty({
        description: 'Start date for statistics',
        example: '2024-03-01T00:00:00.000Z'
    })
    @Transform(({ value }) => {
        if (typeof value === 'string') {
            return new Date(value);
        }
        return value;
    })
    @Type(() => Date)
    @IsDate({ message: 'Start date must be a valid date' })
    @IsNotEmpty({ message: 'Start date is required' })
    start_date: Date;

    @ApiProperty({
        description: 'End date for statistics',
        example: '2024-03-31T23:59:59.999Z'
    })
    @Transform(({ value }) => {
        if (typeof value === 'string') {
            return new Date(value);
        }
        return value;
    })
    @Type(() => Date)
    @IsDate({ message: 'End date must be a valid date' })
    @IsNotEmpty({ message: 'End date is required' })
    end_date: Date;
}

export class BudgetStatisticsQueryDto {
    @ApiProperty({
        description: 'Month (1-12)',
        example: 3,
        minimum: 1,
        maximum: 12
    })
    @Transform(({ value }) => parseInt(value))
    @Type(() => Number)
    @IsInt({ message: 'Month must be an integer' })
    @Min(1, { message: 'Month must be between 1 and 12' })
    @Max(12, { message: 'Month must be between 1 and 12' })
    month: number;

    @ApiProperty({
        description: 'Year',
        example: 2024,
        minimum: 2000,
        maximum: 2100
    })
    @Transform(({ value }) => parseInt(value))
    @Type(() => Number)
    @IsInt({ message: 'Year must be an integer' })
    @Min(2000, { message: 'Year must be between 2000 and 2100' })
    @Max(2100, { message: 'Year must be between 2000 and 2100' })
    year: number;
} 