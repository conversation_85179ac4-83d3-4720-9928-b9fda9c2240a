import { IsDate, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsPositive, IsString, IsUUID } from 'class-validator';
import { GoalStatus, Currency } from '../enums';
import { Transform } from 'class-transformer';

export class CreateGoalDto {
    @IsString()
    @IsNotEmpty()
    name: string;

    @IsNumber()
    @IsPositive()
    target_amount: number;

    @IsNumber()
    current_amount: number;

    @IsDate()
    @Transform(({ value }) => new Date(value))
    start_date: Date;

    @IsDate()
    @Transform(({ value }) => new Date(value))
    target_date: Date;

    @IsEnum(Currency)
    @IsOptional()
    currency?: Currency = Currency.USD;
}

export class UpdateGoalDto {
    @IsString()
    @IsOptional()
    name?: string;

    @IsNumber()
    @IsPositive()
    @IsOptional()
    target_amount?: number;

    @IsNumber()
    @IsOptional()
    current_amount?: number;

    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsOptional()
    start_date?: Date;

    @IsDate()
    @Transform(({ value }) => new Date(value))
    @IsOptional()
    target_date?: Date;

    @IsEnum(GoalStatus)
    @IsOptional()
    status?: GoalStatus;
}

export class GoalQueryDto {
    @IsEnum(GoalStatus)
    @IsOptional()
    status?: GoalStatus;

    @IsNumber()
    @Transform(({ value }) => parseInt(value))
    @IsOptional()
    page?: number = 1;

    @IsNumber()
    @Transform(({ value }) => parseInt(value))
    @IsOptional()
    limit?: number = 10;
}