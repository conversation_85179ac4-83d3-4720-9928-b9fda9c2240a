# 🔐 Google OAuth2 Setup Guide

## 📋 Tổng quan

Hệ thống Google OAuth2 cho phép người dùng đăng nhập bằng tài khoản Google của họ. Toàn bộ quá trình xử lý được thực hiện ở phía server.

## 🔄 Luồng hoạt động

```mermaid
sequenceDiagram
    participant App as Mobile App
    participant Browser as Browser
    participant B<PERSON> as Backend
    participant Google as Google OAuth2
    
    App->>Browser: Mở URL /auth/google
    Browser->>BE: GET /api/v1/auth/google
    BE->>Google: Redirect to Google OAuth2
    Google->>User: Hiển thị trang đăng nhập
    User->>Google: Đăng nhập & cho phép
    Google->>BE: Redirect to /auth/google/callback?code=...
    BE->>Google: Exchange code for tokens
    Google->>BE: Return user info & tokens
    BE->>BE: Create/Update user & Generate JWT
    BE->>App: Redirect to deep link với JWT token
    App->>App: L<PERSON><PERSON> token & chuyển màn hình
```

## 🛠️ Cấu hình

### 1. Google Console Setup

1. Truy cập [Google Cloud Console](https://console.cloud.google.com/)
2. Tạo project mới hoặc chọn project hiện có
3. Bật Google+ API
4. Tạo OAuth 2.0 credentials:
   - **Application type**: Web application
   - **Authorized redirect URIs**: 
     - Development: `https://your-ngrok-url.ngrok-free.app/api/v1/auth/google/callback`
     - Production: `https://vuquangduy.io.vn/api/v1/auth/google/callback`

### 2. Environment Variables

#### Development (.env)
```env
GOOGLE_CLIENT_ID=your-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-client-secret
GOOGLE_CALLBACK_URL=https://your-ngrok-url.ngrok-free.app/api/v1/auth/google/callback
GOOGLE_DEEP_LINK=financeapp://auth/callback
```

#### Production (.env.production)
```env
GOOGLE_CLIENT_ID=your-production-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-production-client-secret
GOOGLE_CALLBACK_URL=https://vuquangduy.io.vn/api/v1/auth/google/callback
GOOGLE_DEEP_LINK=financeapp://auth/callback
```

## 🚀 API Endpoints

### 1. Initiate Google OAuth2
```http
GET /api/v1/auth/google
```
- **Mô tả**: Bắt đầu quá trình đăng nhập Google
- **Response**: HTTP 302 redirect đến Google OAuth2
- **Sử dụng**: Mobile app mở URL này trong browser

### 2. Google OAuth2 Callback
```http
GET /api/v1/auth/google/callback?code=...&state=...
```
- **Mô tả**: Xử lý callback từ Google
- **Response**: HTTP 302 redirect đến mobile app
- **Success**: `financeapp://auth/callback?token=<jwt>&refresh_token=<refresh>`
- **Error**: `financeapp://auth/callback?error=<type>&message=<message>`

## 📱 Mobile App Integration

### 1. Khởi tạo đăng nhập
```javascript
// Mở browser với URL Google OAuth2
const authUrl = 'https://your-domain.com/api/v1/auth/google';
Linking.openURL(authUrl);
```

### 2. Xử lý Deep Link
```javascript
// Lắng nghe deep link callback
Linking.addEventListener('url', handleDeepLink);

function handleDeepLink(event) {
  const url = event.url;
  
  if (url.startsWith('financeapp://auth/callback')) {
    const params = new URLSearchParams(url.split('?')[1]);
    
    if (params.get('token')) {
      // Đăng nhập thành công
      const token = params.get('token');
      const refreshToken = params.get('refresh_token');
      
      // Lưu tokens và chuyển màn hình
      await AsyncStorage.setItem('accessToken', token);
      await AsyncStorage.setItem('refreshToken', refreshToken);
      navigation.navigate('Home');
      
    } else if (params.get('error')) {
      // Đăng nhập thất bại
      const error = params.get('error');
      const message = params.get('message');
      Alert.alert('Đăng nhập thất bại', message);
    }
  }
}
```

## 🔍 Debug & Testing

### 1. Debug Endpoint
```http
GET /api/v1/debug/google-oauth
```
Hiển thị cấu hình Google OAuth2 hiện tại

### 2. Test Flow
1. Truy cập: `http://localhost:8000/api/v1/auth/google`
2. Đăng nhập Google
3. Kiểm tra redirect về deep link
4. Verify JWT token

## ⚠️ Troubleshooting

### Lỗi thường gặp:

1. **"redirect_uri_mismatch"**
   - Kiểm tra GOOGLE_CALLBACK_URL trong .env
   - Đảm bảo URL khớp với Google Console

2. **"invalid_client"**
   - Kiểm tra GOOGLE_CLIENT_ID và GOOGLE_CLIENT_SECRET
   - Đảm bảo credentials đúng environment

3. **Deep link không hoạt động**
   - Kiểm tra mobile app đã register deep link scheme
   - Test deep link bằng adb (Android) hoặc simulator (iOS)

4. **CORS errors**
   - Kiểm tra domain được whitelist trong Google Console
   - Đảm bảo HTTPS cho production

## 📊 Monitoring

Backend sẽ log chi tiết quá trình OAuth2:
- 🔄 Khởi tạo OAuth2 flow
- 📥 Nhận callback từ Google  
- ✅ Tạo/cập nhật user thành công
- 🔗 Redirect về mobile app
- ❌ Các lỗi xảy ra

Kiểm tra logs để debug issues.
