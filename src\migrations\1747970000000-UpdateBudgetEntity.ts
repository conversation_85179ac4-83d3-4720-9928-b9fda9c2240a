import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateBudgetEntity1747970000000 implements MigrationInterface {
    name = 'UpdateBudgetEntity1747970000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if budgets table exists
        const tableExists = await queryRunner.hasTable('budgets');
        
        if (!tableExists) {
            // Create budgets table if it doesn't exist
            await queryRunner.query(`
                CREATE TABLE "budgets" (
                    "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
                    "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                    "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                    "deleted_at" TIMESTAMP,
                    "user_id" uuid NOT NULL,
                    "category_id" uuid NOT NULL,
                    "month" integer NOT NULL,
                    "year" integer NOT NULL,
                    "amount" bigint NOT NULL,
                    "amount_spent" bigint NOT NULL DEFAULT 0,
                    "status" character varying NOT NULL DEFAULT 'active',
                    "is_active" boolean NOT NULL DEFAULT true,
                    CONSTRAINT "PK_budgets" PRIMARY KEY ("id")
                )
            `);

            // Add foreign key constraints
            await queryRunner.query(`
                ALTER TABLE "budgets" 
                ADD CONSTRAINT "FK_budgets_user_id" 
                FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE
            `);

            await queryRunner.query(`
                ALTER TABLE "budgets" 
                ADD CONSTRAINT "FK_budgets_category_id" 
                FOREIGN KEY ("category_id") REFERENCES "categories"("id") ON DELETE RESTRICT
            `);

            // Add indexes for better performance
            await queryRunner.query(`CREATE INDEX "IDX_budgets_user_id" ON "budgets" ("user_id")`);
            await queryRunner.query(`CREATE INDEX "IDX_budgets_category_id" ON "budgets" ("category_id")`);
            await queryRunner.query(`CREATE INDEX "IDX_budgets_month_year" ON "budgets" ("month", "year")`);
            await queryRunner.query(`CREATE INDEX "IDX_budgets_is_active" ON "budgets" ("is_active")`);

            // Add unique constraint to prevent duplicate budgets for same category/month/year
            await queryRunner.query(`
                CREATE UNIQUE INDEX "IDX_budgets_unique_category_period" 
                ON "budgets" ("user_id", "category_id", "month", "year") 
                WHERE "is_active" = true
            `);
        } else {
            // Table exists, check and update columns as needed
            const hasNameColumn = await queryRunner.hasColumn('budgets', 'name');
            const hasStartDateColumn = await queryRunner.hasColumn('budgets', 'start_date');
            const hasEndDateColumn = await queryRunner.hasColumn('budgets', 'end_date');
            const hasMonthColumn = await queryRunner.hasColumn('budgets', 'month');
            const hasYearColumn = await queryRunner.hasColumn('budgets', 'year');
            const hasIsActiveColumn = await queryRunner.hasColumn('budgets', 'is_active');

            // Remove old columns if they exist
            if (hasNameColumn) {
                await queryRunner.query(`ALTER TABLE "budgets" DROP COLUMN "name"`);
            }
            if (hasStartDateColumn) {
                await queryRunner.query(`ALTER TABLE "budgets" DROP COLUMN "start_date"`);
            }
            if (hasEndDateColumn) {
                await queryRunner.query(`ALTER TABLE "budgets" DROP COLUMN "end_date"`);
            }

            // Add new columns if they don't exist
            if (!hasMonthColumn) {
                await queryRunner.query(`ALTER TABLE "budgets" ADD "month" integer NOT NULL DEFAULT 1`);
            }
            if (!hasYearColumn) {
                await queryRunner.query(`ALTER TABLE "budgets" ADD "year" integer NOT NULL DEFAULT 2024`);
            }
            if (!hasIsActiveColumn) {
                await queryRunner.query(`ALTER TABLE "budgets" ADD "is_active" boolean NOT NULL DEFAULT true`);
            }

            // Add amount_spent column if it doesn't exist
            const hasAmountSpentColumn = await queryRunner.hasColumn('budgets', 'amount_spent');
            if (!hasAmountSpentColumn) {
                await queryRunner.query(`ALTER TABLE "budgets" ADD "amount_spent" bigint NOT NULL DEFAULT 0`);
            }

            // Update category_id to be NOT NULL if it's nullable
            const categoryColumn = await queryRunner.getTable('budgets');
            const categoryIdColumn = categoryColumn?.findColumnByName('category_id');
            if (categoryIdColumn?.isNullable) {
                await queryRunner.query(`ALTER TABLE "budgets" ALTER COLUMN "category_id" SET NOT NULL`);
            }

            // Update foreign key constraint for category_id to RESTRICT
            await queryRunner.query(`
                ALTER TABLE "budgets" 
                DROP CONSTRAINT IF EXISTS "FK_budgets_category_id"
            `);
            await queryRunner.query(`
                ALTER TABLE "budgets" 
                ADD CONSTRAINT "FK_budgets_category_id" 
                FOREIGN KEY ("category_id") REFERENCES "categories"("id") ON DELETE RESTRICT
            `);

            // Add indexes if they don't exist
            await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_budgets_month_year" ON "budgets" ("month", "year")`);
            await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_budgets_is_active" ON "budgets" ("is_active")`);

            // Add unique constraint if it doesn't exist
            await queryRunner.query(`
                CREATE UNIQUE INDEX IF NOT EXISTS "IDX_budgets_unique_category_period" 
                ON "budgets" ("user_id", "category_id", "month", "year") 
                WHERE "is_active" = true
            `);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_budgets_unique_category_period"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_budgets_month_year"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_budgets_is_active"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_budgets_category_id"`);
        await queryRunner.query(`DROP INDEX IF EXISTS "IDX_budgets_user_id"`);

        // Drop foreign key constraints
        await queryRunner.query(`ALTER TABLE "budgets" DROP CONSTRAINT IF EXISTS "FK_budgets_category_id"`);
        await queryRunner.query(`ALTER TABLE "budgets" DROP CONSTRAINT IF EXISTS "FK_budgets_user_id"`);

        // For rollback, we'll just drop the table since the old structure was significantly different
        await queryRunner.query(`DROP TABLE IF EXISTS "budgets"`);
    }
}
