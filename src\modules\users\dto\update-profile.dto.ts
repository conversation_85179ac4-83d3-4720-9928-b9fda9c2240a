
import { IsOptional, IsString, IsEnum, IsDateString, IsEmail, IsPhoneNumber } from 'class-validator';
import { UserGender } from '../enums';

export class UpdateProfileDto {
    @IsOptional()
    @IsString()
    firstName?: string;

    @IsOptional()
    @IsString()
    lastName?: string;

    @IsOptional()
    @IsEmail()
    email?: string;

    @IsOptional()
    @IsPhoneNumber(null)
    phone?: string;

    @IsOptional()
    @IsEnum(UserGender)
    gender?: UserGender;

    @IsOptional()
    @IsDateString()
    dateOfBirth?: string;

    @IsOptional()
    @IsString()
    avatar?: string;
}
