/* eslint-disable prettier/prettier */
import { Injectable } from "@nestjs/common";
import { CorsOptions } from "@nestjs/common/interfaces/external/cors-options.interface";
import * as dotenv from "dotenv";
import * as ip from 'ip';
import * as path from 'path';
import * as fs from 'fs';

// Determine environment
process.env.NODE_ENV = process.env.NODE_ENV ?? "development";
const nodeEnv = process.env.NODE_ENV;

// Load environment-specific .env file
const envFiles = [
    `.env.${nodeEnv}`,     // .env.production, .env.development, etc.
    '.env.local',          // Local overrides
    '.env'                 // Default fallback
];

console.log(`🔧 Loading environment: ${nodeEnv}`);

// Load env files in order of priority
envFiles.forEach(envFile => {
    const envPath = path.resolve(process.cwd(), envFile);
    if (fs.existsSync(envPath)) {
        console.log(`📄 Loading env file: ${envFile}`);
        dotenv.config({ path: envPath });
    }
});

console.log("✅ Environment loaded successfully");

const env = Object.assign({}, process.env);

@Injectable()
export class ConfigService {

    NODE_ENV = env.NODE_ENV ?? "development";

    OTP_EXPIRE: number = +env.OTP_EXPIRE || 300; // 5 minutes default

    // DB Mongo
    DB_URI: string = env.DB_URI;

    // DB postgres
    DB_HOST: string = env.DB_HOST;
    DB_PORT: number = +env.DB_PORT || 5432;
    DB_USERNAME: string = env.DB_USERNAME;
    DB_PASSWORD: string = env.DB_PASSWORD;
    DB_NAME: string = env.DB_NAME;
    DB_TYPE: string = env.DB_TYPE || 'postgres';

    // Redis
    REDIS_HOST: string = env.REDIS_HOST;
    REDIS_PORT: number = +env.REDIS_PORT;

    // JWT
    JWT_SECRET = env.JWT_SECRET || 'default-secret-change-in-production'
    AC_TOKEN_EXPIRED = env.AC_TOKEN_EXPIRED || '15m'  // Short-lived for security
    RF_TOKEN_EXPIRED = env.RF_TOKEN_EXPIRED || '7d'

    // Google OAuth
    GOOGLE_CLIENT_ID = env.GOOGLE_CLIENT_ID
    GOOGLE_CLIENT_SECRET = env.GOOGLE_CLIENT_SECRET
    GOOGLE_CALLBACK_URL = env.GOOGLE_CALLBACK_URL

    // Mobile App Deep Link
    MOBILE_APP_SCHEME = env.MOBILE_APP_SCHEME || 'financeapp'
    MOBILE_DEEP_LINK = env.MOBILE_DEEP_LINK || 'financeapp://auth/callback'

    // Security
    BCRYPT_ROUNDS = +env.BCRYPT_ROUNDS || 12

    // Cloudinary
    CLOUDINARY_NAME = env.CLOUDINARY_NAME
    CLOUDINARY_API_KEY = env.CLOUDINARY_API_KEY
    CLOUDINARY_API_SECRET = env.CLOUDINARY_API_SECRET

    // Cookies
    COOKIE_EXPIRED = +env.COOKIE_EXPIRED

    // SPECIAL

    // Expo Development Configuration
    EXPO_HOST = env.EXPO_HOST || '*************:8000'
    EXPO_SCHEME = env.EXPO_SCHEME || 'exp'
    SR = {
        PRODUCT_NAME: 'Finance app',
        VERSION: 'v1.0',
        SIGNATURE: 'Develop finance app by 2H Store',
        SUPPORT: {
            URL: '',
            EMAIL: '<EMAIL>'
        }
    }

    // NETWORD
    LOCAL_IP = ip.address();
    PUBLIC_IP = env.PUBLIC_IP ?? this.LOCAL_IP;
    PORT = parseInt(env.PORT) || 3001;
    HOST = `http://${this.LOCAL_IP}:${this.PORT}`;
    DOMAIN = env.DOMAIN ?? 'https://nguyenquangminh.com';


    // CORS
    CORS: CorsOptions = {
        origin: '*',
        methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'],
        credentials: true,
        allowedHeaders: ['content-type', 'authorization', 'accept', 'origin', 'x-requested-with'],
        exposedHeaders: ['content-type', 'authorization'],
        preflightContinue: false,
        optionsSuccessStatus: 204
    };

    //Providers

    // Nodemailer
    MAIL_HOST: string = env.MAIL_HOST
    MAIL_PORT: number = +env.MAIL_PORT || 587
    MAIL_USER: string = env.MAIL_USER
    MAIL_PASS: string = env.MAIL_PASS

    validateConfig(): void {
        const requiredConfigs = [
            { key: 'DB_HOST', value: this.DB_HOST },
            { key: 'DB_USERNAME', value: this.DB_USERNAME },
            { key: 'DB_PASSWORD', value: this.DB_PASSWORD },
            { key: 'DB_NAME', value: this.DB_NAME },
            { key: 'JWT_SECRET', value: this.JWT_SECRET },
        ];

        const missingConfigs = requiredConfigs.filter(config => !config.value);

        if (missingConfigs.length > 0) {
            const missing = missingConfigs.map(config => config.key).join(', ');
            console.error(`❌ Missing required configuration: ${missing}`);

            if (this.NODE_ENV === 'production') {
                throw new Error(`Missing required configuration in production: ${missing}`);
            } else {
                console.warn(`⚠️  Missing configuration in ${this.NODE_ENV}: ${missing}`);
            }
        }

        // Validate Google OAuth if configured
        if (this.GOOGLE_CLIENT_ID || this.GOOGLE_CLIENT_SECRET) {
            if (!this.GOOGLE_CLIENT_ID || !this.GOOGLE_CLIENT_SECRET || !this.GOOGLE_CALLBACK_URL) {
                console.warn('⚠️  Incomplete Google OAuth configuration');
            }
        }

        // Security warnings
        if (this.NODE_ENV === 'production') {
            if (this.JWT_SECRET === 'default-secret-change-in-production') {
                throw new Error('❌ Default JWT secret detected in production! Please change JWT_SECRET');
            }

            if (this.BCRYPT_ROUNDS < 12) {
                console.warn('⚠️  BCRYPT_ROUNDS should be at least 12 in production');
            }
        }
    }

    logConfig(): void {
        console.log('📋 Configuration Summary:');
        console.log(`   Environment: ${this.NODE_ENV}`);
        console.log(`   Database: ${this.DB_HOST}:${this.DB_PORT}/${this.DB_NAME}`);
        console.log(`   Server: ${this.HOST}`);
        console.log(`   Domain: ${this.DOMAIN}`);
        console.log(`   Google OAuth: ${this.GOOGLE_CLIENT_ID ? '✅ Configured' : '❌ Not configured'}`);
        console.log(`   Deep Link: ${this.MOBILE_DEEP_LINK}`);
        console.log(`   Expo Host: ${this.EXPO_HOST} (${this.EXPO_SCHEME}://)`);
        console.log(`   JWT Expiry: Access=${this.AC_TOKEN_EXPIRED}, Refresh=${this.RF_TOKEN_EXPIRED}`);

        if (this.NODE_ENV !== 'production') {
            console.log(`   Mail: ${this.MAIL_HOST ? '✅ Configured' : '❌ Not configured'}`);
            console.log(`   Cloudinary: ${this.CLOUDINARY_NAME ? '✅ Configured' : '❌ Not configured'}`);
        }
    }

    // Helper methods
    get isDevelopment(): boolean {
        return this.NODE_ENV === 'development';
    }

    get isProduction(): boolean {
        return this.NODE_ENV === 'production';
    }

    get isTest(): boolean {
        return this.NODE_ENV === 'test';
    }

    // End providers
    // Trigger restart 3
}

// Create and initialize config instance
const configInstance = new ConfigService();

// Initialize validation and logging immediately
configInstance.validateConfig();
configInstance.logConfig();

export const config = configInstance;