import { ApiProperty } from '@nestjs/swagger';
import { Transaction } from '../entity/transaction.entity';

export class CreateTransactionResponseDto {
    @ApiProperty({
        description: 'Success status',
        example: true
    })
    success: boolean;

    @ApiProperty({
        description: 'Success message',
        example: 'Transaction created successfully'
    })
    message: string;

    @ApiProperty({
        description: 'Created transaction data',
        type: Transaction
    })
    data: Transaction;
}

export class UpdateTransactionResponseDto {
    @ApiProperty({
        description: 'Success status',
        example: true
    })
    success: boolean;

    @ApiProperty({
        description: 'Success message',
        example: 'Transaction updated successfully'
    })
    message: string;

    @ApiProperty({
        description: 'Updated transaction data',
        type: Transaction
    })
    data: Transaction;
}

export class DeleteTransactionResponseDto {
    @ApiProperty({
        description: 'Success status',
        example: true
    })
    success: boolean;

    @ApiProperty({
        description: 'Success message',
        example: 'Transaction deleted successfully'
    })
    message: string;
} 