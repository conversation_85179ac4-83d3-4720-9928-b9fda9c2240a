# 🔐 Google Token Login API Documentation

## 📋 Overview

API endpoint để xử lý đăng nhập Google OAuth2 bằng access token từ React Native Expo. API sẽ validate token với Google, tạo/cập nhật user và trả về JWT tokens.

## 🚀 API Endpoint

### **POST** `/api/v1/auth/google/token`

Đăng nhập bằng Google access token từ React Native Expo.

## 📥 Request

### **Headers**
```http
Content-Type: application/json
```

### **Body**
```json
{
  "accessToken": "ya29.a0AfH6SMC..."
}
```

### **Request Schema**
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `accessToken` | string | ✅ | Google OAuth2 access token từ Expo |

### **Validation Rules**
- `accessToken`: Không được rỗng, phải là string

## 📤 Response

### **Success Response (200)**
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "John Doe",
    "email": "<EMAIL>",
    "avatar": "https://lh3.googleusercontent.com/a/...",
    "firstName": "John",
    "lastName": "Doe",
    "provide": "google",
    "status": "ACTIVE",
    "role": "USER"
  },
  "tokenType": "Bearer",
  "expiresIn": 900
}
```

### **Response Schema**
| Field | Type | Description |
|-------|------|-------------|
| `accessToken` | string | JWT access token (15 phút) |
| `refreshToken` | string | JWT refresh token (7 ngày) |
| `user` | object | Thông tin user |
| `user.id` | string | User ID (UUID) |
| `user.name` | string | Tên đầy đủ |
| `user.email` | string | Email address |
| `user.avatar` | string | URL ảnh đại diện |
| `user.firstName` | string | Tên |
| `user.lastName` | string | Họ |
| `user.provide` | string | Provider (google/local) |
| `user.status` | string | Trạng thái user |
| `user.role` | string | Vai trò user |
| `tokenType` | string | Loại token (Bearer) |
| `expiresIn` | number | Thời gian hết hạn (giây) |

## ❌ Error Responses

### **400 Bad Request**
```json
{
  "statusCode": 400,
  "message": "Invalid or expired Google access token",
  "error": "Bad Request"
}
```

**Possible error messages:**
- `"Access token is required"`
- `"Invalid or expired Google access token"`
- `"Google account email is not verified"`
- `"Invalid email format from Google"`
- `"Invalid user info from Google: missing email, name"`

### **401 Unauthorized**
```json
{
  "statusCode": 401,
  "message": "Invalid or expired Google access token",
  "error": "Unauthorized"
}
```

### **500 Internal Server Error**
```json
{
  "statusCode": 500,
  "message": "Failed to process Google login",
  "error": "Internal Server Error"
}
```

## 🔄 Flow Diagram

```mermaid
sequenceDiagram
    participant RN as React Native
    participant API as NestJS API
    participant Google as Google API
    participant DB as Database

    RN->>API: POST /auth/google/token
    Note over RN,API: { accessToken: "ya29..." }
    
    API->>Google: GET /oauth2/v3/userinfo
    Note over API,Google: Authorization: Bearer ya29...
    
    Google->>API: User profile data
    Note over Google,API: { sub, email, name, picture, ... }
    
    API->>DB: Check if user exists
    DB->>API: User data or null
    
    alt User exists
        API->>DB: Update user info if needed
    else User not exists
        API->>DB: Create new user
    end
    
    API->>API: Generate JWT tokens
    API->>DB: Save refresh token
    
    API->>RN: Return tokens & user info
    Note over API,RN: { accessToken, refreshToken, user }
```

## 🔧 Implementation Details

### **1. Token Validation Process**
1. Gửi access token đến `https://www.googleapis.com/oauth2/v3/userinfo`
2. Validate response từ Google API
3. Kiểm tra email verification status
4. Validate required fields (sub, email, name)

### **2. User Management**
- **Existing User**: Cập nhật avatar, names, provider nếu cần
- **New User**: Tạo user mới với thông tin từ Google
- **Username Generation**: `{email_prefix}_{random_6_chars}`

### **3. JWT Token Generation**
- **Access Token**: 15 phút expiry
- **Refresh Token**: 7 ngày expiry
- **Payload**: `{ userId, email, username, roles }`

### **4. Security Features**
- Input validation và sanitization
- Error message sanitization
- Request ID tracking
- Comprehensive logging
- Timeout protection (10s)

## 📱 React Native Expo Integration

### **1. Install Dependencies**
```bash
npm install expo-auth-session expo-crypto
```

### **2. Google OAuth Setup**
```javascript
import * as Google from 'expo-auth-session/providers/google';
import * as WebBrowser from 'expo-web-browser';

WebBrowser.maybeCompleteAuthSession();

const [request, response, promptAsync] = Google.useAuthRequest({
  expoClientId: 'YOUR_EXPO_CLIENT_ID',
  iosClientId: 'YOUR_IOS_CLIENT_ID',
  androidClientId: 'YOUR_ANDROID_CLIENT_ID',
  webClientId: 'YOUR_WEB_CLIENT_ID',
});
```

### **3. Handle Authentication**
```javascript
const handleGoogleLogin = async () => {
  try {
    const result = await promptAsync();
    
    if (result?.type === 'success') {
      const { authentication } = result;
      
      // Send access token to your API
      const response = await fetch('https://your-api.com/api/v1/auth/google/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          accessToken: authentication.accessToken,
        }),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        // Save tokens
        await AsyncStorage.setItem('accessToken', data.accessToken);
        await AsyncStorage.setItem('refreshToken', data.refreshToken);
        
        // Navigate to home screen
        navigation.reset({
          index: 0,
          routes: [{ name: 'Home' }],
        });
      } else {
        Alert.alert('Login Failed', data.message);
      }
    }
  } catch (error) {
    console.error('Google login error:', error);
    Alert.alert('Error', 'Failed to login with Google');
  }
};
```

## 🧪 Testing

### **1. Unit Tests**
```bash
npm run test -- google-token-login.spec.ts
```

### **2. Manual Testing**
```bash
# Test with valid token
curl -X POST http://localhost:3000/api/v1/auth/google/token \
  -H "Content-Type: application/json" \
  -d '{"accessToken":"ya29.a0AfH6SMC..."}'

# Test with invalid token
curl -X POST http://localhost:3000/api/v1/auth/google/token \
  -H "Content-Type: application/json" \
  -d '{"accessToken":"invalid-token"}'
```

### **3. Postman Collection**
```json
{
  "name": "Google Token Login",
  "request": {
    "method": "POST",
    "header": [
      {
        "key": "Content-Type",
        "value": "application/json"
      }
    ],
    "body": {
      "mode": "raw",
      "raw": "{\n  \"accessToken\": \"{{google_access_token}}\"\n}"
    },
    "url": {
      "raw": "{{base_url}}/api/v1/auth/google/token",
      "host": ["{{base_url}}"],
      "path": ["api", "v1", "auth", "google", "token"]
    }
  }
}
```

## 🔍 Monitoring & Logging

### **Log Events**
- `🔄 Google token login initiated`
- `✅ Google token login successful`
- `❌ Google token login error`
- `📊 Auth Event: google_token_login`

### **Metrics to Track**
- Success/failure rates
- Response times
- Error types
- User creation vs login rates

## 🛡️ Security Considerations

1. **Token Validation**: Always validate với Google API
2. **Email Verification**: Chỉ accept verified emails
3. **Input Sanitization**: Validate tất cả inputs
4. **Error Handling**: Không expose sensitive info
5. **Rate Limiting**: Implement rate limiting
6. **Logging**: Log security events
7. **Timeout**: Set reasonable timeouts

## 🚀 Production Deployment

### **Environment Variables**
```env
# Required for Google API calls
NODE_ENV=production
JWT_SECRET=your-production-jwt-secret
AC_TOKEN_EXPIRED=15m
RF_TOKEN_EXPIRED=7d
BCRYPT_ROUNDS=12
```

### **Health Check**
```javascript
// Add to health check endpoint
{
  "google_oauth": {
    "status": "healthy",
    "last_check": "2024-01-01T00:00:00Z"
  }
}
```

API này đã sẵn sàng cho production và tích hợp hoàn hảo với React Native Expo!
