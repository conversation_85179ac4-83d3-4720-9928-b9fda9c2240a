# Application
NODE_ENV=development
PORT=8000
DOMAIN=http://localhost:8000

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=finance_app
DB_USERNAME=postgres
DB_PASSWORD=password
DB_TYPE=postgres

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
AC_TOKEN_EXPIRED=15m
RF_TOKEN_EXPIRED=7d

# Google OAuth2 Configuration
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:8000/api/v1/auth/google/callback

# Mobile App Deep Link
MOBILE_APP_SCHEME=financeapp
MOBILE_DEEP_LINK=financeapp://auth/callback

# Expo Development Configuration (for Expo Go)
EXPO_HOST=*************:8000
EXPO_SCHEME=exp

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=10

# Logging
LOG_LEVEL=debug

# CORS
CORS_ORIGIN=*
CORS_CREDENTIALS=true

# OTP Configuration
OTP_EXPIRE=300