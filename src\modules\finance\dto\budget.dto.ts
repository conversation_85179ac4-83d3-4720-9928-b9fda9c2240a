import { <PERSON>N<PERSON>ber, IsOptional, IsUUID, IsInt, Min, Max, IsPositive, IsEnum, IsString, IsArray } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { BudgetStatus, Currency } from '../enums';

export class CreateBudgetDto {
    @ApiProperty({
        description: 'Category ID for the budget',
        example: 'uuid-string'
    })
    @IsUUID()
    categoryId: string;

    @ApiProperty({
        description: 'Month for the budget (1-12)',
        example: 12,
        minimum: 1,
        maximum: 12
    })
    @IsInt()
    @Min(1)
    @Max(12)
    month: number;

    @ApiProperty({
        description: 'Year for the budget',
        example: 2024,
        minimum: 2020
    })
    @IsInt()
    @Min(2020)
    year: number;

    @ApiProperty({
        description: 'Budget amount in the smallest currency unit',
        example: 1000000,
        minimum: 1
    })
    @IsNumber()
    @IsPositive()
    amount: number;

    @ApiPropertyOptional({
        description: 'Currency for the budget',
        enum: Currency,
        example: Currency.USD,
        default: Currency.USD
    })
    @IsOptional()
    @IsEnum(Currency)
    currency?: Currency = Currency.USD;
}

export class UpdateBudgetDto {
    @ApiPropertyOptional({
        description: 'Updated budget amount',
        example: 1500000,
        minimum: 1
    })
    @IsOptional()
    @IsNumber()
    @IsPositive()
    amount?: number;

    @ApiPropertyOptional({
        description: 'Budget status',
        enum: BudgetStatus
    })
    @IsOptional()
    @IsEnum(BudgetStatus)
    status?: BudgetStatus;
}

export class BudgetQueryDto {
    @ApiPropertyOptional({
        description: 'Filter by month (1-12)',
        example: 12
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(1)
    @Max(12)
    month?: number;

    @ApiPropertyOptional({
        description: 'Filter by year',
        example: 2024
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt()
    @Min(2020)
    year?: number;

    @ApiPropertyOptional({
        description: 'Filter by category ID',
        example: 'uuid-string'
    })
    @IsOptional()
    @IsUUID()
    categoryId?: string;

    @ApiPropertyOptional({
        description: 'Filter by status',
        enum: BudgetStatus
    })
    @IsOptional()
    @IsEnum(BudgetStatus)
    status?: BudgetStatus;
}

export class BudgetOverviewDto {
    @ApiProperty({
        description: 'Category ID',
        example: 'uuid-string'
    })
    categoryId: string;

    @ApiProperty({
        description: 'Category name',
        example: 'Food & Dining'
    })
    categoryName: string;

    @ApiProperty({
        description: 'Total budget amount',
        example: 1000000
    })
    budgetAmount: number;

    @ApiProperty({
        description: 'Amount already spent',
        example: 750000
    })
    spentAmount: number;

    @ApiProperty({
        description: 'Remaining budget amount',
        example: 250000
    })
    remainingAmount: number;

    @ApiProperty({
        description: 'Progress percentage (0-100)',
        example: 75
    })
    progressPercent: number;

    @ApiProperty({
        description: 'Warning flag if spending is over 80%',
        example: false
    })
    warning: boolean;
}

export class TransactionSummaryDto {
    @ApiProperty({
        description: 'Transaction ID',
        example: 'uuid-string'
    })
    id: string;

    @ApiProperty({
        description: 'Transaction amount',
        example: 50000
    })
    amount: number;

    @ApiProperty({
        description: 'Transaction date',
        example: '2024-12-01T00:00:00.000Z'
    })
    transaction_date: Date;

    @ApiProperty({
        description: 'Transaction description',
        example: 'Lunch at restaurant'
    })
    description?: string;
}

export class BudgetCategoryReportDto {
    @ApiProperty({
        description: 'Category ID',
        example: 'uuid-string'
    })
    categoryId: string;

    @ApiProperty({
        description: 'Category name',
        example: 'Food & Dining'
    })
    categoryName: string;

    @ApiProperty({
        description: 'Budget amount for this category',
        example: 1000000
    })
    budgetAmount: number;

    @ApiProperty({
        description: 'Amount spent in this category',
        example: 750000
    })
    spentAmount: number;

    @ApiProperty({
        description: 'Remaining amount in this category',
        example: 250000
    })
    remainingAmount: number;

    @ApiProperty({
        description: 'List of transactions in this category',
        type: [TransactionSummaryDto]
    })
    transactions: TransactionSummaryDto[];
}

export class BudgetReportDto {
    @ApiProperty({
        description: 'Total budget for the month',
        example: 5000000
    })
    totalBudget: number;

    @ApiProperty({
        description: 'Total amount spent',
        example: 3750000
    })
    totalSpent: number;

    @ApiProperty({
        description: 'Total remaining budget',
        example: 1250000
    })
    totalRemaining: number;

    @ApiProperty({
        description: 'Budget breakdown by category',
        type: [BudgetCategoryReportDto]
    })
    categories: BudgetCategoryReportDto[];

    @ApiProperty({
        description: 'Report generation date',
        example: '2024-12-01T00:00:00.000Z'
    })
    generatedAt: Date;
}

export class ExportBudgetReportDto {
    @ApiProperty({
        description: 'Month for the report (1-12)',
        example: 12
    })
    @IsInt()
    @Min(1)
    @Max(12)
    month: number;

    @ApiProperty({
        description: 'Year for the report',
        example: 2024
    })
    @IsInt()
    @Min(2020)
    year: number;

    @ApiPropertyOptional({
        description: 'Export format',
        enum: ['csv', 'pdf'],
        example: 'pdf'
    })
    @IsOptional()
    @IsString()
    format?: 'csv' | 'pdf' = 'pdf';
}
