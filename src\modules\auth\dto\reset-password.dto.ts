import { IsNotEmpty, IsString, Length, Matches } from 'class-validator';
import { VerifyOtpDto } from './verify-otp.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ResetPasswordDto extends VerifyOtpDto {
    @ApiProperty()
    @IsString()
    @IsNotEmpty({ message: 'Mật khẩu mới không được để trống' })
    @Length(6, 30, { message: 'Mật khẩu phải có ít nhất 6 ký tự và tối đa 30 ký tự' })
    newPassword: string;
}