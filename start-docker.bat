@echo off
echo 🚀 Starting Docker Desktop...

REM Check if Docker Desktop is already running
docker info >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Docker Desktop is already running!
    goto :end
)

echo 📦 Starting Docker Desktop application...

REM Try to start Docker Desktop
start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"

echo ⏳ Waiting for Docker Desktop to start...
echo This may take 30-60 seconds...

REM Wait for Docker to be ready
:wait_loop
timeout /t 5 /nobreak >nul
docker info >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Docker Desktop is now running!
    goto :end
)

echo ⏳ Still waiting for Docker Desktop...
goto :wait_loop

:end
echo 🎉 Docker Desktop is ready!
echo You can now run: ./deploy-prod.sh
pause
