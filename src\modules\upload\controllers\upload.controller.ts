import {
    Controller,
    Post,
    UploadedFile,
    UploadedFiles,
    UseInterceptors,
    BadRequestException,
} from '@nestjs/common';
import {
    FileInterceptor,
    FilesInterceptor,
} from '@nestjs/platform-express';
import {
    ApiBearerAuth,
    ApiBody,
    ApiConsumes,
    ApiOperation,
    ApiTags,
} from '@nestjs/swagger';
import { UploadService } from '../services/upload.service';
import { multerMemoryOptions } from '../configs/multer.config';

@ApiTags('Upload')
@Controller('upload')
@ApiBearerAuth()
export class UploadController {
    constructor(private readonly uploadService: UploadService) { }

    @Post('single')
    @ApiOperation({ summary: 'Upload single file' })
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                file: {
                    type: 'string',
                    format: 'binary',
                },
            },
        },
    })
    @UseInterceptors(FileInterceptor('file', multerMemoryOptions))
    async uploadSingle(@UploadedFile() file: Express.Multer.File) {
        console.log('🔍 Upload request received:', {
            hasFile: !!file,
            fileName: file?.originalname,
            fileSize: file?.size,
            mimeType: file?.mimetype
        });

        if (!file) {
            throw new BadRequestException('No file uploaded');
        }

        const result = await this.uploadService.uploadToCloud(file);

        return {
            data: {
                url: result.url,
                thumbnail: result.thumbnail,
            },
        };

    }

    @Post('multi')
    @ApiOperation({ summary: 'Upload multiple files (max 5)' })
    @ApiConsumes('multipart/form-data')
    @ApiBody({
        schema: {
            type: 'object',
            properties: {
                files: {
                    type: 'array',
                    items: {
                        type: 'string',
                        format: 'binary',
                    },
                },
            },
        },
    })
    @UseInterceptors(FilesInterceptor('files', 5, multerMemoryOptions))
    async uploadMultiple(@UploadedFiles() files: Express.Multer.File[]) {
        const results = await Promise.all(
            files.map((file) => this.uploadService.uploadToCloud(file)),
        );
        return {
            data: {
                files: results.map((r) => ({
                    url: r.url,
                    thumbnail: r.thumbnail,
                })),
            },
        };
    }
}
