import {
    Injectable,
    NestInterceptor,
    ExecutionContext,
    CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { CurrencyService } from '@modules/finance/services/currency.service';
import { I18nService } from '@modules/finance/services/i18n.service';
import { BASE_CURRENCY } from '@modules/finance/enums/currency.enum';

@Injectable()
export class CurrencyInterceptor implements NestInterceptor {
    constructor(
        private readonly currencyService: CurrencyService,
        private readonly i18nService: I18nService,
    ) {}

    intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
        const request = context.switchToHttp().getRequest();
        const userCurrency = request.currency || 'USD';
        const userLanguage = request.language || 'en';

        return next.handle().pipe(
            map(async (data) => {
                if (!data || typeof data !== 'object') {
                    return data;
                }

                // Transform the response data
                return await this.transformCurrencyFields(data, userCurrency, userLanguage);
            }),
        );
    }

    private async transformCurrencyFields(
        data: any,
        targetCurrency: string,
        language: string,
    ): Promise<any> {
        if (Array.isArray(data)) {
            // Handle arrays
            const transformedArray = await Promise.all(
                data.map(item => this.transformCurrencyFields(item, targetCurrency, language))
            );
            return transformedArray;
        }

        if (data && typeof data === 'object') {
            const transformed = { ...data };

            // Handle pagination wrapper
            if (data.data && Array.isArray(data.data)) {
                transformed.data = await Promise.all(
                    data.data.map(item => this.transformCurrencyFields(item, targetCurrency, language))
                );
                return transformed;
            }

            // Transform currency fields
            await this.transformObjectCurrencyFields(transformed, targetCurrency, language);

            return transformed;
        }

        return data;
    }

    private async transformObjectCurrencyFields(
        obj: any,
        targetCurrency: string,
        language: string,
    ): Promise<void> {
        const currencyFields = [
            'amount',
            'original_amount',
            'target_amount',
            'current_amount',
            'initial_balance',
            'amount_spent',
            'total_planned',
            'total_actual',
            'total_difference',
            'planned_amount',
            'actual_amount',
            'difference_amount'
        ];

        for (const field of currencyFields) {
            if (obj[field] !== undefined && obj[field] !== null) {
                // Convert from base currency to target currency
                const convertedAmount = await this.currencyService.convertFromBaseCurrency(
                    obj[field],
                    targetCurrency
                );

                // Add display fields
                obj[`${field}_display`] = {
                    amount: convertedAmount,
                    currency: targetCurrency,
                    formatted: this.i18nService.formatCurrency(convertedAmount, targetCurrency, language),
                    symbol: this.currencyService.getCurrencySymbol(targetCurrency)
                };

                // Keep original amount info
                obj[`${field}_original`] = {
                    amount: obj[field],
                    currency: BASE_CURRENCY
                };
            }
        }

        // Handle exchange rate display
        if (obj.exchange_rate !== undefined) {
            obj.exchange_rate_display = {
                rate: obj.exchange_rate,
                from: obj.original_currency || BASE_CURRENCY,
                to: BASE_CURRENCY,
                formatted: `1 ${obj.original_currency || BASE_CURRENCY} = ${obj.exchange_rate} ${BASE_CURRENCY}`
            };
        }

        // Handle currency field
        if (obj.currency !== undefined) {
            obj.currency_display = {
                code: obj.currency,
                symbol: this.currencyService.getCurrencySymbol(obj.currency),
                name: this.i18nService.translate(`currency.${obj.currency}`, language)
            };
        }

        // Recursively transform nested objects
        for (const key in obj) {
            if (obj[key] && typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
                // Skip display fields we just created
                if (!key.endsWith('_display') && !key.endsWith('_original')) {
                    await this.transformObjectCurrencyFields(obj[key], targetCurrency, language);
                }
            } else if (Array.isArray(obj[key])) {
                obj[key] = await Promise.all(
                    obj[key].map(item => this.transformCurrencyFields(item, targetCurrency, language))
                );
            }
        }
    }
}
