#!/bin/bash

# Finance App Production Docker Deployment Script
echo "🚀 Starting Finance App Production Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if .env.production exists
if [ ! -f ".env.production" ]; then
    print_error ".env.production file not found!"
    exit 1
fi

print_status "Using production configuration..."
print_info "Domain: https://vuquangduy.online"
print_info "Port: 9005"
print_info "Environment: production"

# Create logs directory if it doesn't exist
print_status "Creating logs directory..."
mkdir -p logs

# Stop and remove existing containers
print_status "Stopping existing containers..."
docker-compose -f docker-compose.prod.yml down

# Remove old images (optional - uncomment if you want to rebuild from scratch)
print_warning "Removing old images..."
docker rmi $(docker images -q finance_app_be_finance-app) 2>/dev/null || true

# Pull latest base images
print_status "Pulling latest base images..."
docker pull node:18-alpine
docker pull postgres:15-alpine

# Build and start containers
print_status "Building and starting containers..."
docker-compose -f docker-compose.prod.yml up -d --build

# Wait for containers to start
print_status "Waiting for containers to start..."
sleep 15

# Show container status
print_status "Container status:"
docker-compose -f docker-compose.prod.yml ps

# Check container health
print_status "Checking container health..."
sleep 10

# Check PostgreSQL health
print_info "Checking PostgreSQL connection..."
docker-compose -f docker-compose.prod.yml exec -T postgres pg_isready -U postgres || print_warning "PostgreSQL not ready yet"

# Check application health
print_info "Checking application health..."
for i in {1..5}; do
    if curl -f http://localhost:9005/api/v1/debug/health 2>/dev/null; then
        print_status "✅ Application is healthy!"
        break
    else
        print_warning "Health check attempt $i/5 failed, retrying in 10 seconds..."
        sleep 10
    fi
done

# Show resource usage
print_status "Container resource usage:"
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

print_status "✅ Production deployment completed successfully!"
echo ""
print_status "🌐 Application URLs:"
print_info "   • Main: https://vuquangduy.online"
print_info "   • Health: https://vuquangduy.online/api/v1/debug/health"
print_info "   • API Docs: https://vuquangduy.online/api/docs"
echo ""
print_status "📋 Management Commands:"
print_info "   • View logs: docker-compose -f docker-compose.prod.yml logs -f finance-app"
print_info "   • Restart: docker-compose -f docker-compose.prod.yml restart finance-app"
print_info "   • Stop: docker-compose -f docker-compose.prod.yml down"
print_info "   • Monitor: docker stats"
echo ""

# Ask if user wants to see logs
read -p "Do you want to see live logs? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    print_status "Showing live logs (press Ctrl+C to exit):"
    docker-compose -f docker-compose.prod.yml logs -f finance-app
fi
