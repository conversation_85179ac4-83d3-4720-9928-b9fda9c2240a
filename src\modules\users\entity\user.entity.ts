import { Column, Entity } from "typeorm";
import { UserGender, UserProvide, UserRole, UserStatus } from "../enums";
import { BaseEntity } from "@base/entity";

@Entity({ name: 'users' })
export class User extends BaseEntity {
    @Column({ unique: true })
    username?: string

    @Column({ nullable: true })
    password?: string

    @Column()
    email: string

    @Column({ nullable: true })
    firstName?: string

    @Column({ nullable: true })
    lastName?: string

    @Column({ nullable: true })
    avatar?: string

    @Column({ type: 'enum', enum: UserGender, default: UserGender.OTHER })
    gender?: UserGender

    @Column({ nullable: true })
    dateOfBirth?: Date

    @Column({ nullable: true })
    phone?: string

    @Column({ type: 'enum', enum: UserStatus, default: UserStatus.ACTIVE })
    status: UserStatus

    @Column({ type: 'enum', enum: UserRole, default: UserRole.USER })
    role: UserRole

    @Column({ nullable: true })
    googleId?: string

    @Column({ type: 'enum', enum: UserProvide, default: UserProvide.LOCAL })
    provide: UserProvide

    @Column({ nullable: true })
    refreshToken?: string;

    @Column({ type: 'decimal', precision: 20, scale: 8, default: 0, transformer: { to: (value) => value, from: (value) => parseFloat(value) } })
    initial_balance: number


    @Column({ default: false })
    isInitialBalance?: boolean;

    @Column({ length: 5, default: 'en' })
    preferred_language: string;

    @Column({ length: 3, default: 'USD' })
    preferred_currency: string;

    @Column({ length: 50, default: 'UTC' })
    timezone: string;
}

