import {
    ExceptionFilter,
    Catch,
    ArgumentsHost,
    HttpException,
    HttpStatus,
    Logger
} from '@nestjs/common';
import { Response } from 'express';
import { QueryFailedError } from 'typeorm';

@Catch()
export class TransactionExceptionFilter implements ExceptionFilter {
    private readonly logger = new Logger(TransactionExceptionFilter.name);

    catch(exception: unknown, host: ArgumentsHost) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse<Response>();
        const request = ctx.getRequest();

        let status = HttpStatus.INTERNAL_SERVER_ERROR;
        let message = 'Internal server error';
        let error = 'Internal Server Error';

        if (exception instanceof HttpException) {
            status = exception.getStatus();
            const errorResponse = exception.getResponse();
            
            if (typeof errorResponse === 'string') {
                message = errorResponse;
            } else if (typeof errorResponse === 'object' && errorResponse !== null) {
                message = (errorResponse as any).message || exception.message;
                error = (errorResponse as any).error || error;
            }
        } else if (exception instanceof QueryFailedError) {
            // Handle database errors
            status = HttpStatus.BAD_REQUEST;
            error = 'Database Error';
            
            if (exception.message.includes('duplicate key')) {
                message = 'A transaction with these details already exists';
            } else if (exception.message.includes('foreign key')) {
                message = 'Invalid category or user reference';
            } else if (exception.message.includes('check constraint')) {
                message = 'Invalid data provided';
            } else {
                message = 'Database operation failed';
            }
        } else if (exception instanceof Error) {
            message = exception.message;
        }

        // Log the error
        this.logger.error(
            `${request.method} ${request.url} - ${status} - ${message}`,
            exception instanceof Error ? exception.stack : exception
        );

        const errorResponse = {
            success: false,
            statusCode: status,
            error,
            message,
            timestamp: new Date().toISOString(),
            path: request.url,
            method: request.method
        };

        response.status(status).json(errorResponse);
    }
} 