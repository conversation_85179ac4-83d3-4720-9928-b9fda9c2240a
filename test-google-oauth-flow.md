# Google OAuth Server-Side Flow Test Guide

## 🔧 Setup Environment

### 1. Environment Variables (.env)
```bash
# Application
NODE_ENV=development
PORT=8000
DOMAIN=http://localhost:8000

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=finance_app
DB_USERNAME=postgres
DB_PASSWORD=password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
AC_TOKEN_EXPIRED=15m
RF_TOKEN_EXPIRED=7d

# Google OAuth2 Configuration
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:8000/api/v1/auth/google/callback

# Mobile App Deep Link
MOBILE_APP_SCHEME=financeapp
MOBILE_DEEP_LINK=financeapp://auth/callback
```

### 2. Google Cloud Console Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing
3. Enable Google+ API
4. Go to Credentials → Create Credentials → OAuth 2.0 Client IDs
5. Application type: Web application
6. Authorized redirect URIs: `http://localhost:8000/api/v1/auth/google/callback`

## 🚀 Testing Flow

### 1. Start Backend Server
```bash
npm run dev
# Server should start on http://localhost:8000
```

### 2. Test OAuth Initiation
Open browser and navigate to:
```
http://localhost:8000/api/v1/auth/google
```

**Expected Result:**
- Redirects to Google OAuth consent screen
- User sees Google login page

### 3. Complete OAuth Flow
1. Login with Google account
2. Grant permissions
3. Google redirects to callback URL

**Expected Success Result:**
```
financeapp://auth/callback?accessToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&refreshToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&status=success
```

**Expected Error Result:**
```
financeapp://auth/callback?status=error&message=Authentication%20failed
```

## 📱 Mobile App Integration

### React Native Deep Link Handling
```javascript
import { Linking } from 'react-native';

// Listen for deep links
Linking.addEventListener('url', handleDeepLink);

function handleDeepLink(event) {
  const url = event.url;
  
  if (url.startsWith('financeapp://auth/callback')) {
    const params = new URLSearchParams(url.split('?')[1]);
    
    if (params.get('status') === 'success') {
      const accessToken = params.get('accessToken');
      const refreshToken = params.get('refreshToken');
      
      // Store tokens and navigate to main app
      storeTokens(accessToken, refreshToken);
      navigateToMainApp();
    } else {
      const errorMessage = params.get('message');
      showError(errorMessage);
    }
  }
}

// Initiate OAuth
function initiateGoogleOAuth() {
  const oauthUrl = 'http://localhost:8000/api/v1/auth/google';
  Linking.openURL(oauthUrl);
}
```

## 🔍 Debug & Logs

### Backend Logs to Watch
```
🔄 Initiating Google OAuth2 flow
🔍 Validating Google OAuth2 profile
👤 Processing user with email: <EMAIL>
✅ Google OAuth2 validation successful for user: <EMAIL>
🔑 Tokens generated successfully
✅ Google OAuth login successful for user: <EMAIL>
🔗 Redirecting to deep link: financeapp://auth/callback?accessToken=***&refreshToken=***&status=success
```

### Common Issues & Solutions

1. **"Missing Google OAuth configuration"**
   - Check GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, GOOGLE_CALLBACK_URL in .env

2. **"Invalid redirect URI"**
   - Ensure callback URL in Google Console matches GOOGLE_CALLBACK_URL

3. **"No user data from Google OAuth"**
   - Check Google+ API is enabled
   - Verify OAuth scopes include 'email', 'profile', 'openid'

4. **Deep link not working**
   - Ensure mobile app is configured to handle financeapp:// scheme
   - Test deep link manually: `adb shell am start -W -a android.intent.action.VIEW -d "financeapp://auth/callback?status=success" com.yourapp`

## 📊 API Endpoints

### 1. Initiate OAuth
```http
GET /api/v1/auth/google
```
- Redirects to Google OAuth consent screen

### 2. OAuth Callback
```http
GET /api/v1/auth/google/callback?code=...&state=...
```
- Handles Google callback
- Creates/finds user in database
- Generates JWT tokens
- Redirects to mobile app via deep link

## 🛡️ Security Features

- ✅ Input validation and sanitization
- ✅ Security headers (HSTS, CSP, etc.)
- ✅ Error message sanitization
- ✅ Request ID tracking for audit
- ✅ JWT token security (short-lived access, long-lived refresh)
- ✅ BCRYPT password hashing
- ✅ CORS configuration

## 📝 Database Changes

The Google OAuth flow will:
1. Create new user with `provide: GOOGLE` if not exists
2. Update existing LOCAL user to GOOGLE provider if email matches
3. Store refresh token hash in database
4. Set user status to ACTIVE

User entity fields used:
- `email` (from Google profile)
- `firstName` (from Google profile.name.givenName)
- `lastName` (from Google profile.name.familyName)
- `avatar` (from Google profile.photos[0].value)
- `googleId` (from Google profile.id)
- `provide` (set to GOOGLE)
- `refreshToken` (hashed refresh token)
