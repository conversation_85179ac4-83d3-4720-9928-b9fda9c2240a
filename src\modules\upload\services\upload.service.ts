import { Injectable } from '@nestjs/common';
import { v2 as cloudinary } from 'cloudinary';
import { Readable } from 'stream';

@Injectable()
export class UploadService {
    async uploadToCloud(file: Express.Multer.File): Promise<{ url: string; thumbnail: string }> {
        return new Promise((resolve, reject) => {
            const stream = cloudinary.uploader.upload_stream(
                { folder: 'uploads' },
                (error, result) => {
                    if (error) return reject(error);
                    const url = result.secure_url;
                    const public_id = result.public_id;

                    const thumbnail = cloudinary.url(public_id, {
                        width: 100,
                        height: 100,
                        crop: 'fill',
                        secure: true,
                    });

                    resolve({ url, thumbnail });
                },
            );

            Readable.from(file.buffer).pipe(stream);
        });
    }
}
