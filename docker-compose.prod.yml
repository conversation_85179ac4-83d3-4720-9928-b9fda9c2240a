version: '3.8'

services:
  finance-app:
    build: .
    container_name: finance_app_prod
    ports:
      - "9005:9005"
    environment:
      - NODE_ENV=production
      - PORT=9005
      - DOMAIN=https://vuquangduy.online
    env_file:
      - .env.production
    volumes:
      - ./logs:/app/logs
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    restart: unless-stopped
    depends_on:
      - postgres
    networks:
      - finance-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9005/api/v1/debug/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    image: postgres:15-alpine
    container_name: finance_postgres_prod
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: Duy0509@
      POSTGRES_DB: postgres
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - pgdata_prod:/var/lib/postgresql/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    networks:
      - finance-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  pgdata_prod:
    driver: local

networks:
  finance-network:
    driver: bridge
