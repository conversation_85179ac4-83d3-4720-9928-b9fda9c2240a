import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Patch,
    Post,
    Put,
    Query,
} from "@nestjs/common";
import { CategoryService } from "../services/category.service";
import { CreateCategoryDto, FindAllCategoryDto, FindOneCategoryDto, UpdateCategoryDto } from "../dto/category.dto";
import { queryObjects } from "v8";
import { CurrentUser } from "@base/api/decorators";
import { IUser } from "@modules/users/interfaces";
import { ApiBearerAuth, ApiTags } from "@nestjs/swagger";
import { IdParamDto } from "@base/api/dto";

@ApiTags("Categories")
@ApiBearerAuth()
@Controller("categories")
export class CategoryController {
    constructor(private readonly categoryService: CategoryService) { }

    @Post()
    async create(@Body() body: CreateCategoryDto, @CurrentUser() user: IUser) {
        return await this.categoryService.create(body, user.userId);
    }

    @Patch(":id")
    async update(@Param("id") id: string, @Body() body: UpdateCategoryDto, @CurrentUser() user: IUser) {
        return await this.categoryService.update(id, body, user.userId);
    }

    @Delete(":id")
    async delete(@Param("id") id: string, @CurrentUser() user: IUser) {
        return await this.categoryService.delete(id, user.userId);
    }

    @Get()
    async getAll(
        @Query() query: FindAllCategoryDto,
        @CurrentUser() user: IUser,
    ) {
        return await this.categoryService.getAll(query, user.userId);
    }


    @Get(":id")
    async getOne(
        @Param("id") id: string,
        @CurrentUser() user: IUser,
        @Query() query: FindOneCategoryDto,
    ) {
        return await this.categoryService.getOne(id, query, user.userId);
    }
}
