---
description: 
globs: 
alwaysApply: false
---
<PERSON><PERSON><PERSON> trúc dự án: 
src/modules: Each feature is a module placed in its own folder, which includes subfolders:

controllers: Handles requests, calls services, returns responses.

services: Contains business logic.

repositories: Handles database queries (especially complex ones).

dto: Validates input data.

entities: Describes the database table.

interfaces: Defines interfaces used within the module.

validators: Contains custom input validation functions using decorators.

constants (if needed): Declares module-specific constants.

src/base: Base shared across the entire system:

api: Core components like base services, helpers, interceptors, decorators, base DTOs, etc.

entity, database, swagger, otp, redis: Core functionalities grouped accordingly.

src/config: Manages all environment-based configurations:

A shared ConfigService is used across the system.

May include TypeORM configurations (including for migrations).

src/migrations: Contains database migration files.

src/provider: Integrates third-party services (e.g., Firebase, Cloudinary, Stripe...).

src/shared: Contains shared utilities that are not part of base (helpers, constants, pipes, filters, guards...).

src/types: Defines global types used across the project.

🎯 Controller
Handles receiving requests and sending responses only.

Do not write business logic in controllers.

Use NestJS decorators: @Get, @Post, @Patch, @Delete.

Follow RESTful naming: findAll, findOne, create, update, remove.

Inject services to process requests.

🔧 Service
Contains all business logic.

Should not directly interact with the database.

Use @Injectable() to make it available for injection.

Can break large functions into private helper methods.

📦 Repository
Handles complex database queries.

Use @Injectable() and define methods for specific query purposes: search, joins, custom queries.

Avoid writing raw SQL in services if you can encapsulate it in a repository method.

🧱 Entity
Each entity represents a database table.

Use decorators: @Entity, @Column, @OneToMany, @ManyToOne, @JoinColumn, etc.

Define data types, length, nullability, uniqueness...

Do not include business logic in entities.

📨 DTO (Data Transfer Object)
Used to validate incoming request data.

Do not use entities as DTOs.

File naming convention: create-[feature].dto.ts, update-[feature].dto.ts.

Use decorators: @IsString, @IsNotEmpty, @IsOptional, @IsNumber, etc.

🧪 Validator
Contains custom validators.

Use @ValidatorConstraint() to implement custom validation logic.

Apply validators to DTO fields using decorators.

⚙️ Nest CLI
Use the NestJS CLI to generate modules, controllers, services, DTOs... to ensure consistency:
nest generate module [name]
nest generate controller [name]
nest generate service [name]
Avoid creating files manually if CLI can be used.

🌐 Global Configuration
Use @nestjs/config to read .env files.

Configure ValidationPipe globally in main.ts.

Set up Swagger, global interceptors, middleware, and guards in main.ts or app.module.ts.

🔐 Authentication & Security
Use JWT for authentication.

Define JwtStrategy, JwtGuard, and custom guards as needed.

Store JWT secret keys in .env.

Avoid hard-coding any security-related credentials.

📄 Swagger
Use Swagger to generate API documentation.

Decorators used:

@ApiTags(): Tag by module.

@ApiOperation(): Describe the operation.

@ApiResponse(): Define response schema.

Register Swagger in main.ts.

🧪 Testing
Use Jest or Supertest.

Write unit tests for services.

Write end-to-end (e2e) tests for controllers.

🛠️ Code Configuration
.eslintrc.js: Defines code rules (anti-patterns, styles, logic errors...).

.prettierrc: Defines formatting rules (spacing, quotes, line breaks...).

tsconfig.json: TypeScript compiler settings (strictness, module resolution...).