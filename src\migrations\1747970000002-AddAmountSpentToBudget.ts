import { MigrationInterface, QueryRunner } from "typeorm";

export class AddAmountSpentToBudget1747970000002 implements MigrationInterface {
    name = 'AddAmountSpentToBudget1747970000002'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add amount_spent column with default 0
        await queryRunner.query(`ALTER TABLE "budgets" ADD "amount_spent" bigint NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "budgets" DROP COLUMN "amount_spent"`);
    }
} 