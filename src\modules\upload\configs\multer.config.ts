// src/common/multer/multer.config.ts
import { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
import { memoryStorage } from 'multer';
import { extname } from 'path';
import { BadRequestException } from '@nestjs/common';

const ALLOWED_EXTENSIONS = [
    '.jpg', '.jpeg', '.png', '.webp', '.svg',      // image
    '.pdf',                                    // pdf
    '.doc', '.docx',                           // word
    '.xls', '.xlsx',                           // excel
    '.csv',                                    // csv
];

const ALLOWED_MIME_TYPES = [
    'image/jpeg', 'image/png', 'image/webp',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/csv',
];

export const multerFileFilter = (
    req: Express.Request,
    file: Express.Multer.File,
    callback: (error: Error | null, acceptFile: boolean) => void,
) => {
    const ext = extname(file.originalname).toLowerCase();
    const mime = file.mimetype;

    if (ALLOWED_EXTENSIONS.includes(ext) && ALLOWED_MIME_TYPES.includes(mime)) {
        callback(null, true);
    } else {
        callback(
            new BadRequestException(`File format not supported: ${ext}, ${mime}`),
            false,
        );
    }
};

export const multerMemoryOptions: MulterOptions = {
    storage: memoryStorage(),
    fileFilter: multerFileFilter,
    limits: {
        fileSize: 10 * 1024 * 1024, // tối đa 10MB
    },
};
