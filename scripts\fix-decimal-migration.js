const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function fixDecimalMigration() {
    const client = new Client({
        host: 'localhost',
        port: 5432,
        database: 'postgres',
        user: 'postgres',
        password: 'Duy0509@'
    });

    try {
        await client.connect();
        console.log('✅ Connected to database');

        // Read SQL file
        const sqlPath = path.join(__dirname, '..', 'fix-null-amounts.sql');
        const sql = fs.readFileSync(sqlPath, 'utf8');

        // Split by semicolon and execute each statement
        const statements = sql.split(';').filter(stmt => stmt.trim());

        for (const statement of statements) {
            if (statement.trim()) {
                console.log(`🔧 Executing: ${statement.trim().substring(0, 50)}...`);
                await client.query(statement);
            }
        }

        console.log('✅ All SQL statements executed successfully');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    } finally {
        await client.end();
        console.log('🔌 Database connection closed');
    }
}

fixDecimalMigration();
