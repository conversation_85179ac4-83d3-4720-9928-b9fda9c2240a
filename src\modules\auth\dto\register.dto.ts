import { UserGender } from '@modules/users/enums';
import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsOptional, IsString, IsDateString, IsNotEmpty } from 'class-validator';

export class RegisterDto {
    @ApiProperty()
    @IsString()
    username: string;

    @ApiProperty()
    @IsString()
    password: string;

    @ApiProperty()
    @IsEmail()
    email: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    firstName: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    lastName: string;

    @ApiProperty()
    @IsEnum(UserGender)
    @IsOptional()
    gender?: UserGender;

    @ApiProperty()
    @IsDateString()
    @IsNotEmpty()
    dateOfBirth?: string;

    @ApiProperty()
    @IsString()
    @IsNotEmpty()
    phone?: string;
}
