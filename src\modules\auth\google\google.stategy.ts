import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { Injectable, Logger, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { config } from '@config';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from '@modules/users/entity';
import { Repository } from 'typeorm';
import { UserProvide, UserRole, UserStatus } from '@modules/users/enums';
import { GoogleProfile, OAuthValidationResult, SecurityContext } from '../interfaces/google-oauth.interface';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';
import { GoogleProfileDto } from '../dto/google-oauth.dto';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
    private readonly logger = new Logger(GoogleStrategy.name);

    constructor(
        @InjectRepository(User) private readonly userRepository: Repository<User>
    ) {
        super({
            clientID: config.GOOGLE_CLIENT_ID,
            clientSecret: config.GOOGLE_CLIENT_SECRET,
            callbackURL: config.GOOGLE_CALLBACK_URL,
            scope: ['email', 'profile', 'openid'],
            passReqToCallback: true, // Enable request object in validate method
        });

        this.validateConfiguration();
        this.logger.log('🔧 Google OAuth2 Strategy initialized successfully');
    }

    private validateConfiguration(): void {
        const requiredConfigs = [
            { key: 'GOOGLE_CLIENT_ID', value: config.GOOGLE_CLIENT_ID },
            { key: 'GOOGLE_CLIENT_SECRET', value: config.GOOGLE_CLIENT_SECRET },
            { key: 'GOOGLE_CALLBACK_URL', value: config.GOOGLE_CALLBACK_URL },
        ];

        const missingConfigs = requiredConfigs.filter(cfg => !cfg.value);

        if (missingConfigs.length > 0) {
            const missing = missingConfigs.map(cfg => cfg.key).join(', ');
            this.logger.error(`❌ Missing required Google OAuth configuration: ${missing}`);
            throw new Error(`Missing Google OAuth configuration: ${missing}`);
        }

        this.logger.debug(`✅ Google OAuth configuration validated`);
        this.logger.debug(`Client ID: ${config.GOOGLE_CLIENT_ID?.substring(0, 20)}...`);
        this.logger.debug(`Callback URL: ${config.GOOGLE_CALLBACK_URL}`);
    }

    async validate(
        req: any,
        accessToken: string,
        refreshToken: string,
        profile: GoogleProfile,
        done: VerifyCallback
    ): Promise<any> {
        try {
            this.logger.log('🔍 Validating Google OAuth2 profile');
            this.logger.debug(`Profile ID: ${profile.id}`);

            // Extract security context
            const securityContext: SecurityContext = {
                userAgent: req?.headers?.['user-agent'],
                ipAddress: req?.ip || req?.connection?.remoteAddress,
                timestamp: Date.now(),
            };

            // Validate profile data
            const validationResult = await this.validateGoogleProfile(profile);
            if (!validationResult.isValid) {
                this.logger.error(`❌ Invalid Google profile: ${validationResult.error}`);
                return done(new BadRequestException(validationResult.error), null);
            }

            const { name, emails, photos } = profile;

            if (!emails || emails.length === 0) {
                this.logger.error('❌ No email found in Google profile');
                return done(new BadRequestException('No email found in Google profile'), null);
            }

            const email = emails[0].value;
            const emailVerified = emails[0].verified || false;

            this.logger.log(`👤 Processing user with email: ${email}`);

            // Check for existing user (both Google and Local provider)
            let user = await this.findOrCreateUser(profile, email, emailVerified, securityContext);

            if (!user) {
                this.logger.error('❌ Failed to create or find user');
                return done(new InternalServerErrorException('Failed to process user'), null);
            }

            this.logger.log(`✅ Google OAuth2 validation successful for user: ${user.email}`);

            // Add OAuth tokens to user object for later use (extend user object)
            const userWithTokens = {
                ...user,
                oauthTokens: {
                    accessToken,
                    refreshToken,
                    provider: 'google'
                }
            };

            done(null, userWithTokens);

        } catch (error) {
            this.logger.error('❌ Google OAuth2 validation error:', error);
            this.logger.error('Error stack:', error.stack);
            done(error, null);
        }
    }

    private async validateGoogleProfile(profile: GoogleProfile): Promise<OAuthValidationResult> {
        try {
            // Basic validation
            if (!profile.id || !profile.emails || profile.emails.length === 0) {
                return {
                    isValid: false,
                    error: 'Invalid profile: missing required fields'
                };
            }

            // Email validation
            const email = profile.emails[0].value;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                return {
                    isValid: false,
                    error: 'Invalid email format'
                };
            }

            // Additional security checks
            if (profile.id.length < 10) {
                return {
                    isValid: false,
                    error: 'Invalid Google ID format'
                };
            }

            return { isValid: true };

        } catch (error) {
            this.logger.error('Profile validation error:', error);
            return {
                isValid: false,
                error: 'Profile validation failed'
            };
        }
    }

    private async findOrCreateUser(
        profile: GoogleProfile,
        email: string,
        _emailVerified: boolean,
        _securityContext: SecurityContext
    ): Promise<User | null> {
        try {
            // Check for existing user with same email (any provider)
            let user = await this.userRepository.findOne({
                where: [
                    { email, provide: UserProvide.GOOGLE },
                    { email, provide: UserProvide.LOCAL }
                ]
            });

            if (user) {
                this.logger.log(`✅ Existing user found: ${user.id}`);

                // Update Google-specific fields if user exists with LOCAL provider
                if (user.provide === UserProvide.LOCAL) {
                    this.logger.log('🔄 Converting LOCAL user to GOOGLE provider');
                    user.provide = UserProvide.GOOGLE;
                    user.googleId = profile.id;

                    // Update avatar if not set
                    if (!user.avatar && profile.photos && profile.photos.length > 0) {
                        user.avatar = profile.photos[0].value;
                    }

                    user = await this.userRepository.save(user);
                }

                return user;
            }

            // Create new user
            this.logger.log('🆕 Creating new Google user');

            const { name, photos } = profile;
            const username = await this.generateUniqueUsername(profile.id, email);

            const newUser = this.userRepository.create({
                email: email,
                firstName: name?.givenName || '',
                lastName: name?.familyName || '',
                avatar: photos && photos.length > 0 ? photos[0].value : null,
                googleId: profile.id,
                provide: UserProvide.GOOGLE,
                status: UserStatus.ACTIVE,
                username: username,
                role: UserRole.USER,
                initial_balance: 0,
                isInitialBalance: false,
            });

            user = await this.userRepository.save(newUser);
            this.logger.log(`✅ New user created: ${user.id}`);

            return user;

        } catch (error) {
            this.logger.error('❌ Error in findOrCreateUser:', error);
            throw new InternalServerErrorException('Failed to process user data');
        }
    }

    private async generateUniqueUsername(googleId: string, email: string): Promise<string> {
        const baseUsername = `google_${googleId}`;

        // Check if username already exists
        const existingUser = await this.userRepository.findOne({
            where: { username: baseUsername }
        });

        if (!existingUser) {
            return baseUsername;
        }

        // Generate alternative username using email prefix
        const emailPrefix = email.split('@')[0];
        const alternativeUsername = `${emailPrefix}_${googleId.substring(0, 8)}`;

        return alternativeUsername;
    }
}