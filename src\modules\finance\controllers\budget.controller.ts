import {
    Controller,
    Get,
    Post,
    Patch,
    Delete,
    Body,
    Param,
    Query,
    Req,
    Res,
    HttpCode,
    HttpStatus,
    ValidationPipe,
    UsePipes
} from '@nestjs/common';
import { Response } from 'express';
import { DataSource } from 'typeorm';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiQuery } from '@nestjs/swagger';
import { BudgetService } from '../services/budget.service';
import {
    CreateBudgetDto,
    UpdateBudgetDto,
    BudgetQueryDto,
    BudgetOverviewDto,
    BudgetReportDto,
    ExportBudgetReportDto
} from '../dto/budget.dto';
import { Budget } from '../entity/budget.entity';
import { Transaction } from '../entity/transaction.entity';
import { TransactionType } from '../enums';

@ApiTags('Budgets')
@ApiBearerAuth()
@Controller('budgets')
@UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
export class BudgetController {
    constructor(
        private readonly budgetService: BudgetService,
        private readonly dataSource: DataSource
    ) {}

    @Post()
    @ApiOperation({
        summary: 'Create a new budget',
        description: 'Create a budget for a specific category, month, and year. Validates that total budget does not exceed financial capacity.'
    })
    @ApiResponse({
        status: 201,
        description: 'Budget created successfully',
        type: Budget
    })
    @ApiResponse({
        status: 400,
        description: 'Bad request - validation failed or budget exceeds capacity'
    })
    @ApiResponse({
        status: 404,
        description: 'Category not found'
    })
    @ApiResponse({
        status: 409,
        description: 'Budget already exists for this category and period'
    })
    async createBudget(@Body() createBudgetDto: CreateBudgetDto): Promise<Budget> {
        return await this.budgetService.createBudget(createBudgetDto);
    }

    @Get()
    @ApiOperation({
        summary: 'Get budgets',
        description: 'Retrieve budgets with optional filtering by month, year, category, or status'
    })
    @ApiQuery({ name: 'month', required: false, description: 'Filter by month (1-12)' })
    @ApiQuery({ name: 'year', required: false, description: 'Filter by year' })
    @ApiQuery({ name: 'categoryId', required: false, description: 'Filter by category ID' })
    @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
    @ApiResponse({
        status: 200,
        description: 'Budgets retrieved successfully',
        type: [Budget]
    })
    async getBudgets(@Query() query: BudgetQueryDto): Promise<Budget[]> {
        return await this.budgetService.getBudgets(query);
    }

    @Get('overview')
    @ApiOperation({
        summary: 'Get budget overview',
        description: 'Get budget overview with spending progress for a specific month and year'
    })
    @ApiQuery({ name: 'month', required: true, description: 'Month (1-12)' })
    @ApiQuery({ name: 'year', required: true, description: 'Year' })
    @ApiResponse({
        status: 200,
        description: 'Budget overview retrieved successfully',
        type: [BudgetOverviewDto]
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid month or year'
    })
    async getBudgetOverview(
        @Query('month') month: number,
        @Query('year') year: number
    ): Promise<BudgetOverviewDto[]> {
        return await this.budgetService.getBudgetOverview(month, year);
    }

    @Get('report')
    @ApiOperation({
        summary: 'Get budget report',
        description: 'Generate detailed budget report for a specific month and year'
    })
    @ApiQuery({ name: 'month', required: true, description: 'Month (1-12)' })
    @ApiQuery({ name: 'year', required: true, description: 'Year' })
    @ApiResponse({
        status: 200,
        description: 'Budget report generated successfully',
        type: BudgetReportDto
    })
    async getBudgetReport(
        @Query('month') month: number,
        @Query('year') year: number
    ): Promise<BudgetReportDto> {
        return await this.budgetService.getBudgetReport(month, year);
    }

    @Post('export')
    @HttpCode(HttpStatus.OK)
    @ApiOperation({
        summary: 'Export budget report',
        description: 'Export budget report as CSV or PDF file'
    })
    @ApiResponse({
        status: 200,
        description: 'Budget report exported successfully'
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid export parameters'
    })
    async exportBudgetReport(
        @Body(new ValidationPipe({
            transform: true,
            whitelist: true,
            forbidNonWhitelisted: true,
            validationError: { target: false, value: false }
        })) exportDto: ExportBudgetReportDto,
        @Res() res: Response,
        @Req() req: any
    ): Promise<void> {
        console.log('🔍 Export Budget Raw Body:', req.body);
        console.log('🔍 Export Budget Parsed DTO:', JSON.stringify(exportDto, null, 2));
        return await this.budgetService.exportBudgetReport(exportDto, res);
    }

    @Get(':id')
    @ApiOperation({
        summary: 'Get budget by ID',
        description: 'Retrieve a specific budget by its ID'
    })
    @ApiParam({ name: 'id', description: 'Budget ID' })
    @ApiResponse({
        status: 200,
        description: 'Budget retrieved successfully',
        type: Budget
    })
    @ApiResponse({
        status: 404,
        description: 'Budget not found'
    })
    async getBudgetById(@Param('id') id: string): Promise<Budget> {
        return await this.budgetService.getBudgetById(id);
    }

    @Patch(':id')
    @ApiOperation({
        summary: 'Update budget',
        description: 'Update budget amount or status. Validates total budget capacity when increasing amount.'
    })
    @ApiParam({ name: 'id', description: 'Budget ID' })
    @ApiResponse({
        status: 200,
        description: 'Budget updated successfully',
        type: Budget
    })
    @ApiResponse({
        status: 400,
        description: 'Bad request - validation failed or budget exceeds capacity'
    })
    @ApiResponse({
        status: 404,
        description: 'Budget not found'
    })
    async updateBudget(
        @Param('id') id: string,
        @Body() updateBudgetDto: UpdateBudgetDto
    ): Promise<Budget> {
        return await this.budgetService.updateBudget(id, updateBudgetDto);
    }

    @Delete(':id')
    @HttpCode(HttpStatus.NO_CONTENT)
    @ApiOperation({
        summary: 'Delete budget',
        description: 'Soft delete a budget (sets is_active to false)'
    })
    @ApiParam({ name: 'id', description: 'Budget ID' })
    @ApiResponse({
        status: 204,
        description: 'Budget deleted successfully'
    })
    @ApiResponse({
        status: 404,
        description: 'Budget not found'
    })
    async deleteBudget(@Param('id') id: string): Promise<void> {
        return await this.budgetService.deleteBudget(id);
    }

    @Get('debug/routes')
    @ApiOperation({
        summary: 'Debug - List all budget routes',
        description: 'Debug endpoint to show all available budget routes'
    })
    async debugRoutes() {
        return {
            message: 'Budget API Routes',
            baseUrl: 'http://*************:8000',
            globalPrefix: 'api/v1',
            controllerPath: 'budgets',
            routes: [
                'GET /api/v1/budgets - List budgets',
                'GET /api/v1/budgets/overview?month=5&year=2025 - Budget overview',
                'GET /api/v1/budgets/report?month=5&year=2025 - Budget report',
                'POST /api/v1/budgets - Create budget',
                'POST /api/v1/budgets/export - Export report',
                'GET /api/v1/budgets/:id - Get budget by ID',
                'PATCH /api/v1/budgets/:id - Update budget',
                'DELETE /api/v1/budgets/:id - Delete budget',
                'GET /api/v1/budgets/debug/routes - This debug endpoint'
            ],
            note: 'Make sure your client is calling the correct URLs without duplicate /api/v1 prefix'
        };
    }

    @Get('debug/data')
    @ApiOperation({
        summary: 'Debug - Check budget vs transaction data',
        description: 'Debug endpoint to compare budget amount_spent with actual transaction totals'
    })
    async debugData(@Req() req: any) {
        const userId = req.user.id;

        // Get raw budget data from repository
        const rawBudgets = await this.dataSource.getRepository(Budget).find({
            where: { user_id: userId, is_active: true },
            relations: ['category']
        });

        // Get transaction data for comparison
        const transactions = await this.dataSource.getRepository(Transaction).find({
            where: {
                user_id: userId,
                type: TransactionType.EXPENSE
            },
            relations: ['category'],
            order: { transaction_date: 'DESC' }
        });

        // Calculate actual spent per category per month
        const categorySpent = new Map();
        transactions.forEach(t => {
            if (t.category_id) {
                const month = t.transaction_date.getMonth() + 1;
                const year = t.transaction_date.getFullYear();
                const key = `${t.category_id}-${month}-${year}`;
                const current = categorySpent.get(key) || 0;
                categorySpent.set(key, current + t.amount);
            }
        });

        return {
            summary: {
                total_budgets: rawBudgets.length,
                total_transactions: transactions.length,
                total_expense_amount: transactions.reduce((sum, t) => sum + t.amount, 0)
            },
            budgets: rawBudgets.map(b => {
                const key = `${b.category_id}-${b.month}-${b.year}`;
                const actualSpent = categorySpent.get(key) || 0;
                return {
                    id: b.id,
                    category: b.category?.name,
                    month: `${b.month}/${b.year}`,
                    budget_amount: b.amount,
                    amount_spent_in_db: b.amount_spent,
                    actual_spent_calculated: actualSpent,
                    difference: b.amount_spent - actualSpent,
                    is_correct: b.amount_spent === actualSpent
                };
            }),
            transactions_by_category: Array.from(categorySpent.entries()).map(([key, amount]) => {
                const [categoryId, month, year] = key.split('-');
                const transaction = transactions.find(t => t.category_id === categoryId);
                return {
                    key,
                    category_id: categoryId,
                    category_name: transaction?.category?.name,
                    month_year: `${month}/${year}`,
                    total_amount: amount
                };
            })
        };
    }

    @Get('debug/raw-data')
    @ApiOperation({
        summary: 'Debug - Raw data without auth',
        description: 'Debug endpoint to check raw budget and transaction data'
    })
    async debugRawData() {
        // Get all budgets
        const rawBudgets = await this.dataSource.getRepository(Budget).find({
            where: { is_active: true },
            relations: ['category', 'user']
        });

        // Get all expense transactions
        const transactions = await this.dataSource.getRepository(Transaction).find({
            where: { type: TransactionType.EXPENSE },
            relations: ['category', 'user'],
            order: { transaction_date: 'DESC' }
        });

        // Calculate actual spent per budget
        const budgetAnalysis = rawBudgets.map(budget => {
            const budgetTransactions = transactions.filter(t =>
                t.user_id === budget.user_id &&
                t.category_id === budget.category_id &&
                t.transaction_date.getMonth() + 1 === budget.month &&
                t.transaction_date.getFullYear() === budget.year
            );

            const actualSpent = budgetTransactions.reduce((sum, t) => sum + t.amount, 0);

            return {
                budget_id: budget.id,
                user_email: budget.user?.email,
                category: budget.category?.name,
                period: `${budget.month}/${budget.year}`,
                budget_amount: budget.amount,
                amount_spent_in_db: budget.amount_spent,
                actual_spent_calculated: actualSpent,
                difference: budget.amount_spent - actualSpent,
                is_correct: budget.amount_spent === actualSpent,
                transaction_count: budgetTransactions.length,
                transactions: budgetTransactions.map(t => ({
                    id: t.id,
                    amount: t.amount,
                    date: t.transaction_date,
                    description: t.description
                }))
            };
        });

        return {
            summary: {
                total_budgets: rawBudgets.length,
                total_transactions: transactions.length,
                incorrect_budgets: budgetAnalysis.filter(b => !b.is_correct).length
            },
            budget_analysis: budgetAnalysis
        };
    }
}
