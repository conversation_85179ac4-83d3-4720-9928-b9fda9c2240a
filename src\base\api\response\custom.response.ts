import { Call<PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from "@nestjs/common";
import { map, Observable } from "rxjs";
import { Reflector } from "@nestjs/core";

@Injectable()
export class CustomResponseInterceptor implements NestInterceptor {
    constructor(private reflect: Reflector) { }

    //   private getCustomStatusCode(statusCode: number): number {
    //     const statusCodeMapping = {
    //       200: 1000,
    //       201: 1001,
    //       202: 1002,
    //       204: 1004,
    //       400: 1001,
    //       404: 1002,
    //       500: 1003,
    //     };
    //     return statusCodeMapping[statusCode] || statusCode;
    //   }

    intercept(context: ExecutionContext, next: <PERSON><PERSON>and<PERSON>): Observable<any> | Promise<Observable<any>> {
        const responseMessage = this.reflect.get<string>('response_message', context.getHandler())
        const statusCode = context.switchToHttp().getResponse().statusCode

        return next.handle().pipe(map((response) => {
            // Handle different response formats
            let data, meta;

            if (response && typeof response === 'object' && response.hasOwnProperty('data')) {
                // Response already has data/meta structure
                data = response.data;
                meta = response.metadata || response.meta || {};
            } else {
                // Response is direct data (array, object, etc.)
                data = response;
                meta = {};
            }

            return {
                status: 'success',
                statusCode: statusCode,
                message: responseMessage || "",
                data: data || {},
                meta: meta,
                timestamp: new Date().toISOString(),
            };
        }))

    }
}