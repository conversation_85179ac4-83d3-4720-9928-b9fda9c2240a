import { Controller, Get, Param } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { DataSource } from 'typeorm';
import { Budget } from '@modules/finance/entity/budget.entity';
import { Transaction } from '@modules/finance/entity/transaction.entity';
import { TransactionType } from '@modules/finance/enums';
import { Public } from '@modules/auth/jwt/jwt.decorator';

@ApiTags('Debug')
@Controller('debug')
@Public()
export class DebugController {
    constructor(private readonly dataSource: DataSource) {}
    
    @Get('routes')
    @ApiOperation({ 
        summary: 'Debug - List all API routes',
        description: 'Debug endpoint to show all available API routes'
    })
    async getAllRoutes() {
        return {
            message: 'Finance App API Routes',
            baseUrl: 'http://*************:8000',
            globalPrefix: 'api/v1',
            note: 'Make sure your client is calling the correct URLs without duplicate /api/v1 prefix',
            routes: {
                auth: [
                    'POST /api/v1/auth/register',
                    'POST /api/v1/auth/login',
                    'POST /api/v1/auth/refresh-token',
                    'POST /api/v1/auth/forgot-password',
                    'POST /api/v1/auth/verify-otp',
                    'POST /api/v1/auth/reset-password',
                    'GET /api/v1/auth/google',
                    'GET /api/v1/auth/google/redirect'
                ],
                users: [
                    'POST /api/v1/users/initial-balance',
                    'GET /api/v1/users/profile'
                ],
                categories: [
                    'GET /api/v1/categories',
                    'POST /api/v1/categories',
                    'GET /api/v1/categories/:id',
                    'PATCH /api/v1/categories/:id',
                    'DELETE /api/v1/categories/:id'
                ],
                goals: [
                    'GET /api/v1/goals',
                    'POST /api/v1/goals',
                    'GET /api/v1/goals/:id',
                    'PATCH /api/v1/goals/:id',
                    'DELETE /api/v1/goals/:id'
                ],
                budgets: [
                    'GET /api/v1/budgets',
                    'POST /api/v1/budgets',
                    'GET /api/v1/budgets/overview?month=5&year=2025',
                    'GET /api/v1/budgets/report?month=5&year=2025',
                    'POST /api/v1/budgets/export',
                    'GET /api/v1/budgets/:id',
                    'PATCH /api/v1/budgets/:id',
                    'DELETE /api/v1/budgets/:id',
                    'GET /api/v1/budgets/debug/routes'
                ],
                debug: [
                    'GET /api/v1/debug/routes - This endpoint'
                ]
            },
            commonIssues: {
                duplicatePrefix: {
                    wrong: '/api/v1/api/v1/budgets/overview',
                    correct: '/api/v1/budgets/overview',
                    solution: 'Check your client baseURL configuration'
                },
                authentication: {
                    header: 'Authorization: Bearer <your-jwt-token>',
                    note: 'Most endpoints require authentication except /auth and /debug'
                }
            }
        };
    }

    @Get('health')
    @ApiOperation({
        summary: 'Health check',
        description: 'Simple health check endpoint'
    })
    async healthCheck() {
        return {
            status: 'OK',
            timestamp: new Date().toISOString(),
            service: 'Finance App API',
            version: '1.0.0'
        };
    }

    @Get('budget-data')
    @ApiOperation({
        summary: 'Debug - Check budget vs transaction data',
        description: 'Debug endpoint to analyze budget amount_spent vs actual transaction totals'
    })
    async debugBudgetData() {
        // Get all budgets
        const rawBudgets = await this.dataSource.getRepository(Budget).find({
            where: { is_active: true },
            relations: ['category', 'user']
        });

        // Get all expense transactions
        const transactions = await this.dataSource.getRepository(Transaction).find({
            where: { type: TransactionType.EXPENSE },
            relations: ['category', 'user'],
            order: { transaction_date: 'DESC' }
        });

        // Calculate actual spent per budget
        const budgetAnalysis = rawBudgets.map(budget => {
            const budgetTransactions = transactions.filter(t =>
                t.user_id === budget.user_id &&
                t.category_id === budget.category_id &&
                t.transaction_date.getMonth() + 1 === budget.month &&
                t.transaction_date.getFullYear() === budget.year
            );

            const actualSpent = budgetTransactions.reduce((sum, t) => sum + t.amount, 0);

            return {
                budget_id: budget.id,
                user_email: budget.user?.email,
                category: budget.category?.name,
                period: `${budget.month}/${budget.year}`,
                budget_amount: budget.amount,
                amount_spent_in_db: budget.amount_spent,
                actual_spent_calculated: actualSpent,
                difference: budget.amount_spent - actualSpent,
                is_correct: budget.amount_spent === actualSpent,
                transaction_count: budgetTransactions.length,
                transactions: budgetTransactions.map(t => ({
                    id: t.id,
                    amount: t.amount,
                    date: t.transaction_date,
                    description: t.description
                }))
            };
        });

        const incorrectBudgets = budgetAnalysis.filter(b => !b.is_correct);

        return {
            summary: {
                total_budgets: rawBudgets.length,
                total_transactions: transactions.length,
                incorrect_budgets: incorrectBudgets.length,
                accuracy_percentage: rawBudgets.length > 0 ?
                    Math.round(((rawBudgets.length - incorrectBudgets.length) / rawBudgets.length) * 100) : 100
            },
            incorrect_budgets: incorrectBudgets,
            all_budgets: budgetAnalysis
        };
    }

    @Get('reset-budget/:budgetId')
    @ApiOperation({
        summary: 'Debug - Reset budget amount_spent to 0',
        description: 'Reset a specific budget amount_spent to 0 for testing'
    })
    async resetBudget(@Param('budgetId') budgetId: string) {
        const budget = await this.dataSource.getRepository(Budget).findOne({
            where: { id: budgetId }
        });

        if (!budget) {
            return { error: 'Budget not found' };
        }

        const oldAmount = budget.amount_spent;
        budget.amount_spent = 0;
        await this.dataSource.getRepository(Budget).save(budget);

        return {
            message: 'Budget reset successfully',
            budget_id: budgetId,
            old_amount: oldAmount,
            new_amount: 0
        };
    }
}
