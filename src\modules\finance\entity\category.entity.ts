import { BaseEntity } from "@base/entity";
import { User } from "@modules/users/entity";
import { Column, Entity, JoinColumn, ManyToOne, Unique } from "typeorm";
import { CategoryStatus, CategoryType } from "../enums";

@Entity({ name: 'categories' })
@Unique(['name', 'user'])
export class Category extends BaseEntity {
    @ManyToOne(() => User, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'user_id' })
    user: User;

    @Column({ type: 'uuid' })
    user_id: string;

    @Column()
    name: string;

    @Column({
        type: 'enum',
        enum: CategoryType,
        nullable: false,
    })
    type: CategoryType;

    @Column({
        type: 'enum',
        enum: CategoryStatus,
        default: CategoryStatus.ACTIVE,
    })
    status: CategoryStatus;

}