import {
    <PERSON><PERSON><PERSON>,
    Column,
    ManyToOne,
    <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
    OneToMany,
} from 'typeorm';
import { BaseEntity } from '@base/entity';
import { User } from '@modules/users/entity';
import { ReportDetail } from './report-detail.entity';

@Entity({ name: 'reports' })
export class Report extends BaseEntity {
    @Column({ type: 'uuid' })
    user_id: string;

    @ManyToOne(() => User, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'user_id' })
    user: User;

    @Column()
    period: string;

    @Column({ type: 'bigint', transformer: { to: (value) => value, from: (value) => parseInt(value) } })
    total_planned: number;

    @Column({ type: 'bigint', transformer: { to: (value) => value, from: (value) => parseInt(value) } })
    total_actual: number;

    @Column({ type: 'bigint', transformer: { to: (value) => value, from: (value) => parseInt(value) } })
    total_difference: number;

    @Column({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
    generated_at: Date;

    @OneToMany(() => ReportDetail, (detail) => detail.report)
    report_details: ReportDetail[];
}
