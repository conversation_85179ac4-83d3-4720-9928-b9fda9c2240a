Write-Host "🚀 Starting Docker Desktop..." -ForegroundColor Green

# Check if Docker Desktop is already running
try {
    docker info | Out-Null
    Write-Host "✅ Docker Desktop is already running!" -ForegroundColor Green
    exit 0
} catch {
    Write-Host "📦 Docker Desktop is not running, starting it..." -ForegroundColor Yellow
}

# Try to start Docker Desktop
$dockerPath = "C:\Program Files\Docker\Docker\Docker Desktop.exe"
if (Test-Path $dockerPath) {
    Write-Host "🔧 Starting Docker Desktop application..." -ForegroundColor Blue
    Start-Process -FilePath $dockerPath
    
    Write-Host "⏳ Waiting for Docker Desktop to start..." -ForegroundColor Yellow
    Write-Host "This may take 30-60 seconds..." -ForegroundColor Yellow
    
    # Wait for Docker to be ready (max 2 minutes)
    $timeout = 120
    $elapsed = 0
    
    do {
        Start-Sleep -Seconds 5
        $elapsed += 5
        
        try {
            docker info | Out-Null
            Write-Host "✅ Docker Desktop is now running!" -ForegroundColor Green
            Write-Host "🎉 Docker Desktop is ready!" -ForegroundColor Green
            Write-Host "You can now run: ./deploy-prod.sh" -ForegroundColor Cyan
            exit 0
        } catch {
            Write-Host "⏳ Still waiting for Docker Desktop... ($elapsed/$timeout seconds)" -ForegroundColor Yellow
        }
    } while ($elapsed -lt $timeout)
    
    Write-Host "❌ Timeout waiting for Docker Desktop to start" -ForegroundColor Red
    Write-Host "Please check Docker Desktop manually" -ForegroundColor Red
    exit 1
    
} else {
    Write-Host "❌ Docker Desktop not found at: $dockerPath" -ForegroundColor Red
    Write-Host "Please install Docker Desktop or check the installation path" -ForegroundColor Red
    exit 1
}
