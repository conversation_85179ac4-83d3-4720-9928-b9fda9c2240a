import {
    <PERSON><PERSON><PERSON>,
    Column,
    <PERSON><PERSON>o<PERSON><PERSON>,
    Jo<PERSON><PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { BaseEntity } from '@base/entity';
import { Category } from './category.entity';
import { Report } from './report.entity';

@Entity({ name: 'report_details' })
export class ReportDetail extends BaseEntity {
    @Column({ type: 'uuid' })
    report_id: string;

    @ManyToOne(() => Report, (report) => report.report_details, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'report_id' })
    report: Report;

    @Column({ type: 'uuid' })
    category_id: string;

    @ManyToOne(() => Category, { onDelete: 'SET NULL', nullable: true })
    @JoinColumn({ name: 'category_id' })
    category: Category;

    @Column({ type: 'bigint', transformer: { to: (value) => value, from: (value) => parseInt(value) } })
    planned_amount: number;

    @Column({ type: 'bigint', transformer: { to: (value) => value, from: (value) => parseInt(value) } })
    actual_amount: number;

    @Column({ type: 'bigint', transformer: { to: (value) => value, from: (value) => parseInt(value) } })
    difference_amount: number;

    @Column({ type: 'decimal', precision: 5, scale: 2 })
    completion_percentage: number;
}
