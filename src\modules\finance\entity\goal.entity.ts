import { BaseEntity } from "@base/entity";
import { User } from "@modules/users/entity";
import { Column, <PERSON>tity, JoinColumn, ManyToOne, Table } from "typeorm";
import { GoalStatus } from "../enums";

@Entity({ name: "goals" })
export class Goal extends BaseEntity {

    @ManyToOne(() => User, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'user_id' })
    user: User;

    @Column({ type: 'uuid' })
    user_id: string;

    @Column()
    name: string

    @Column({ type: 'bigint', nullable: false, transformer: { to: (value) => value, from: (value) => parseInt(value) } })
    target_amount: number

    @Column({ type: 'bigint', nullable: false, transformer: { to: (value) => value, from: (value) => parseInt(value) } })
    current_amount: number

    @Column()
    start_date: Date

    @Column()
    target_date: Date

    @Column({
        type: 'enum',
        enum: GoalStatus,
        default: GoalStatus.ACTIVE,
    })
    status: GoalStatus;

    @Column({ length: 3, default: 'USD' })
    currency: string;
}