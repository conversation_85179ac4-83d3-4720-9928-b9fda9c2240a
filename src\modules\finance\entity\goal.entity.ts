import { BaseEntity } from "@base/entity";
import { User } from "@modules/users/entity";
import { Column, <PERSON><PERSON><PERSON>, Join<PERSON>olum<PERSON>, ManyToOne, Table } from "typeorm";
import { GoalStatus } from "../enums";

@Entity({ name: "goals" })
export class Goal extends BaseEntity {

    @ManyToOne(() => User, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'user_id' })
    user: User;

    @Column({ type: 'uuid' })
    user_id: string;

    @Column()
    name: string

    @Column({ type: 'decimal', precision: 20, scale: 8, nullable: false, transformer: { to: (value) => value, from: (value) => parseFloat(value) } })
    target_amount: number

    @Column({ type: 'decimal', precision: 20, scale: 8, nullable: false, transformer: { to: (value) => value, from: (value) => parseFloat(value) } })
    current_amount: number

    @Column()
    start_date: Date

    @Column()
    target_date: Date

    @Column({
        type: 'enum',
        enum: GoalStatus,
        default: GoalStatus.ACTIVE,
    })
    status: GoalStatus;

    @Column({ length: 3, default: 'USD' })
    currency: string;
}