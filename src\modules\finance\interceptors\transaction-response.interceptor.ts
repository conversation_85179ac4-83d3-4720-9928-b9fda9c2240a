import {
    Injectable,
    NestInterceptor,
    Execution<PERSON><PERSON>x<PERSON>,
    CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface StandardResponse<T> {
    success: boolean;
    message: string;
    data: T;
    timestamp: string;
}

@Injectable()
export class TransactionResponseInterceptor<T> implements NestInterceptor<T, StandardResponse<T>> {
    intercept(context: ExecutionContext, next: CallHandler): Observable<StandardResponse<T>> {
        const request = context.switchToHttp().getRequest();
        const method = request.method;
        const url = request.url;

        return next.handle().pipe(
            map(data => {
                let message = 'Operation completed successfully';
                
                // Customize message based on HTTP method and URL
                if (method === 'POST') {
                    message = 'Transaction created successfully';
                } else if (method === 'PATCH' || method === 'PUT') {
                    message = 'Transaction updated successfully';
                } else if (method === 'DELETE') {
                    message = 'Transaction deleted successfully';
                } else if (method === 'GET') {
                    if (url.includes('statistics')) {
                        message = 'Statistics retrieved successfully';
                    } else if (url.includes('/transactions/')) {
                        message = 'Transaction retrieved successfully';
                    } else {
                        message = 'Transactions retrieved successfully';
                    }
                }

                return {
                    success: true,
                    message,
                    data,
                    timestamp: new Date().toISOString(),
                };
            }),
        );
    }
} 