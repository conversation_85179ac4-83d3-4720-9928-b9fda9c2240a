#!/bin/bash

echo "🔍 Checking Docker status..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed!"
    exit 1
fi

print_status "Docker is installed: $(docker --version)"

# Check if Docker daemon is running
if docker info >/dev/null 2>&1; then
    print_status "✅ Docker daemon is running"
    
    # Show Docker info
    echo ""
    print_status "Docker system info:"
    docker info --format "{{.ServerVersion}}" 2>/dev/null && echo "Server Version: $(docker info --format "{{.ServerVersion}}" 2>/dev/null)"
    docker info --format "{{.Architecture}}" 2>/dev/null && echo "Architecture: $(docker info --format "{{.Architecture}}" 2>/dev/null)"
    
    # Check if containers are running
    echo ""
    print_status "Current containers:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    
    echo ""
    print_status "🎉 Docker is ready for deployment!"
    
else
    print_error "❌ Docker daemon is not running!"
    echo ""
    print_warning "Please start Docker Desktop:"
    echo "  1. Open Docker Desktop application"
    echo "  2. Wait for it to start completely"
    echo "  3. Run this script again to verify"
    echo ""
    print_warning "Or if using Linux, start Docker service:"
    echo "  sudo systemctl start docker"
    echo "  sudo systemctl enable docker"
    
    exit 1
fi
