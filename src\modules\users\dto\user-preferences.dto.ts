import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsString, MaxLength } from 'class-validator';
import { Currency, Language } from '@modules/finance/enums';

export class UpdateUserPreferencesDto {
    @ApiPropertyOptional({
        description: 'Preferred language',
        enum: Language,
        example: Language.VI
    })
    @IsOptional()
    @IsEnum(Language, { message: 'Language must be one of: en, vi, zh' })
    preferred_language?: Language;

    @ApiPropertyOptional({
        description: 'Preferred currency',
        enum: Currency,
        example: Currency.VND
    })
    @IsOptional()
    @IsEnum(Currency, { message: 'Currency must be one of: USD, VND, CNY' })
    preferred_currency?: Currency;

    @ApiPropertyOptional({
        description: 'Timezone',
        example: 'Asia/Ho_Chi_Minh'
    })
    @IsOptional()
    @IsString({ message: 'Timezone must be a string' })
    @MaxLength(50, { message: 'Timezone cannot exceed 50 characters' })
    timezone?: string;
}
