export interface ChartDataPoint {
    label: string;
    value: number;
    date?: Date;
}

export interface TrendData {
    period: string;
    income: number;
    expense: number;
    balance: number;
    growth?: number; // % tăng trưởng so với kỳ trước
}

export interface ComparisonPeriod {
    period: string;
    income: number;
    expense: number;
    balance: number;
    transactionCount: number;
}

export interface CategoryData {
    categoryId: string;
    categoryName: string;
    amount: number;
    percentage: number;
    color?: string;
}

export interface FinancialTrendPoint {
    period: string;
    income: number;
    expense: number;
    balance: number;
    growth: number;
    movingAverage?: number;
}
