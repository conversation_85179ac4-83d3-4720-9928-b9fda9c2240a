import { Injectable, NotFoundException, Inject, Scope } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { User } from '../entity';
import { SetInitialBalanceDto, InitialBalanceResponseDto } from '../dto';
import { IUser } from '../interfaces';
import { UpdateProfileDto } from '../dto/update-profile.dto';
import { UpdateUserPreferencesDto } from '../dto/user-preferences.dto';

@Injectable({ scope: Scope.REQUEST })
export class UserService {
    constructor(
        @InjectRepository(User)
        private userRepository: Repository<User>,
        @Inject(REQUEST) private request: Request
    ) { }

    private get currentUser(): IUser {
        return this.request.user as IUser;
    }

    private get userId(): string {
        return this.currentUser.userId;
    }

    async setInitialBalance(setInitialBalanceDto: SetInitialBalanceDto): Promise<{ data: InitialBalanceResponseDto }> {
        // Find the current user
        const user = await this.userRepository.findOne({
            where: { id: this.userId }
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        // Update initial balance and set isInitialBalance to true
        user.initial_balance = setInitialBalanceDto.initial_balance;
        user.isInitialBalance = true;

        const updatedUser = await this.userRepository.save(user);

        return {
            data: {
                userId: updatedUser.id,
                initial_balance: updatedUser.initial_balance,
                currency: setInitialBalanceDto.currency,
                isInitialBalance: updatedUser.isInitialBalance,
                updated_at: updatedUser.updated_at
            }
        };
    }

    async getProfile(): Promise<{ data: User }> {
        const user = await this.userRepository.findOne({
            where: { id: this.userId }
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        return {
            data: user
        };
    }

    async updateProfile(dto: UpdateProfileDto): Promise<{ data: User }> {
        const user = await this.userRepository.findOne({ where: { id: this.userId } });
        if (!user) throw new NotFoundException('User not found');

        Object.assign(user, dto);
        const updatedUser = await this.userRepository.save(user);
        return { data: updatedUser };
    }

    async updatePreferences(dto: UpdateUserPreferencesDto): Promise<{ data: User }> {
        const user = await this.userRepository.findOne({ where: { id: this.userId } });
        if (!user) throw new NotFoundException('User not found');

        // Update preferences
        if (dto.preferred_language !== undefined) {
            user.preferred_language = dto.preferred_language;
        }
        if (dto.preferred_currency !== undefined) {
            user.preferred_currency = dto.preferred_currency;
        }
        if (dto.timezone !== undefined) {
            user.timezone = dto.timezone;
        }

        const updatedUser = await this.userRepository.save(user);
        return { data: updatedUser };
    }

    async getPreferences(): Promise<{ data: { preferred_language: string; preferred_currency: string; timezone: string } }> {
        const user = await this.userRepository.findOne({
            where: { id: this.userId },
            select: ['preferred_language', 'preferred_currency', 'timezone']
        });

        if (!user) throw new NotFoundException('User not found');

        return {
            data: {
                preferred_language: user.preferred_language,
                preferred_currency: user.preferred_currency,
                timezone: user.timezone
            }
        };
    }
}
