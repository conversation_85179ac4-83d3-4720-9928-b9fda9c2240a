import {
    Controller,
    Get,
    Query,
    ValidationPipe,
    UseGuards
} from '@nestjs/common';
import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiQuery,
    ApiBearerAuth
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/jwt/jwt-auth.guard';
import { CurrentUser } from '../../../base/api/decorators/current-user.decorator';
import { IUser } from '../../users/interfaces/user.interface';
import { StatisticsService } from '../services/statistics.service';
import {
    IncomeExpenseChartQueryDto,
    CategoryAnalysisQueryDto,
    FinancialTrendsQueryDto,
    ComparisonQueryDto,
    IncomeExpenseChartDto,
    CategoryAnalysisDto,
    FinancialTrendsDto,
    ComparisonDto
} from '../dto/statistics.dto';

@ApiTags('Statistics')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('statistics')
export class StatisticsController {
    constructor(private readonly statisticsService: StatisticsService) {}

    @Get('income-expense-chart')
    @ApiOperation({
        summary: 'Get income vs expense chart data',
        description: 'Returns data for income and expense visualization over time periods (week, month, year)'
    })
    @ApiQuery({
        name: 'period',
        enum: ['week', 'month', 'year'],
        description: 'Time period for chart data',
        required: true
    })
    @ApiQuery({
        name: 'startDate',
        type: String,
        description: 'Start date for custom range (ISO string)',
        required: false
    })
    @ApiQuery({
        name: 'endDate',
        type: String,
        description: 'End date for custom range (ISO string)',
        required: false
    })
    @ApiResponse({
        status: 200,
        description: 'Income expense chart data retrieved successfully',
        type: IncomeExpenseChartDto,
        schema: {
            example: {
                status: 'success',
                statusCode: 200,
                message: 'Income expense chart data retrieved successfully',
                data: {
                    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                    incomeData: [1000000, 1500000, 1200000, 1800000],
                    expenseData: [800000, 1200000, 900000, 1400000],
                    totalIncome: 5500000,
                    totalExpense: 4300000,
                    netAmount: 1200000
                },
                meta: {},
                timestamp: '2024-12-31T10:00:00.000Z'
            }
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid query parameters'
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - Invalid or missing JWT token'
    })
    async getIncomeExpenseChart(
        @Query(new ValidationPipe({
            transform: true,
            whitelist: true,
            forbidNonWhitelisted: true,
            validationError: { target: false, value: false }
        })) queryDto: IncomeExpenseChartQueryDto,
        @CurrentUser() user: IUser
    ) {
        const data = await this.statisticsService.getIncomeExpenseChart(user.userId, queryDto);
        return {
            data,
            message: 'Income expense chart data retrieved successfully'
        };
    }

    @Get('category-analysis')
    @ApiOperation({
        summary: 'Get category analysis data',
        description: 'Returns spending/income breakdown by categories for pie/donut charts'
    })
    @ApiQuery({
        name: 'period',
        enum: ['month', 'year'],
        description: 'Time period for analysis',
        required: true
    })
    @ApiQuery({
        name: 'type',
        enum: ['income', 'expense'],
        description: 'Transaction type filter',
        required: false
    })
    @ApiQuery({
        name: 'month',
        type: Number,
        description: 'Month (1-12) for monthly analysis',
        required: false
    })
    @ApiQuery({
        name: 'year',
        type: Number,
        description: 'Year for analysis',
        required: false
    })
    @ApiResponse({
        status: 200,
        description: 'Category analysis data retrieved successfully',
        type: CategoryAnalysisDto,
        schema: {
            example: {
                status: 'success',
                statusCode: 200,
                message: 'Category analysis data retrieved successfully',
                data: {
                    categories: [
                        {
                            categoryId: 'uuid-1',
                            categoryName: 'Food & Dining',
                            amount: 500000,
                            percentage: 25.5,
                            color: '#FF6384'
                        },
                        {
                            categoryId: 'uuid-2',
                            categoryName: 'Transportation',
                            amount: 300000,
                            percentage: 15.3,
                            color: '#36A2EB'
                        }
                    ],
                    totalAmount: 2000000,
                    period: '2024-12'
                },
                meta: {},
                timestamp: '2024-12-31T10:00:00.000Z'
            }
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid query parameters'
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - Invalid or missing JWT token'
    })
    async getCategoryAnalysis(
        @Query(new ValidationPipe({
            transform: true,
            whitelist: true,
            forbidNonWhitelisted: true,
            validationError: { target: false, value: false }
        })) queryDto: CategoryAnalysisQueryDto,
        @CurrentUser() user: IUser
    ) {
        const data = await this.statisticsService.getCategoryAnalysis(user.userId, queryDto);
        return {
            data,
            message: 'Category analysis data retrieved successfully'
        };
    }

    @Get('financial-trends')
    @ApiOperation({
        summary: 'Get financial trends data',
        description: 'Returns financial trend analysis with growth rates and moving averages'
    })
    @ApiQuery({
        name: 'period',
        enum: ['month', 'year'],
        description: 'Time period for trend analysis',
        required: true
    })
    @ApiQuery({
        name: 'limit',
        type: Number,
        description: 'Number of periods to analyze (default: 12 for month, 5 for year)',
        required: false
    })
    @ApiResponse({
        status: 200,
        description: 'Financial trends data retrieved successfully',
        type: FinancialTrendsDto,
        schema: {
            example: {
                status: 'success',
                statusCode: 200,
                message: 'Financial trends data retrieved successfully',
                data: {
                    trends: [
                        {
                            period: '2024-01',
                            income: 3000000,
                            expense: 2500000,
                            balance: 500000,
                            growth: 5.2,
                            movingAverage: 480000
                        }
                    ],
                    overallGrowth: 12.5,
                    averageBalance: 520000
                },
                meta: {},
                timestamp: '2024-12-31T10:00:00.000Z'
            }
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid query parameters'
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - Invalid or missing JWT token'
    })
    async getFinancialTrends(
        @Query(new ValidationPipe({
            transform: true,
            whitelist: true,
            forbidNonWhitelisted: true,
            validationError: { target: false, value: false }
        })) queryDto: FinancialTrendsQueryDto,
        @CurrentUser() user: IUser
    ) {
        const data = await this.statisticsService.getFinancialTrends(user.userId, queryDto);
        return {
            data,
            message: 'Financial trends data retrieved successfully'
        };
    }

    @Get('comparison')
    @ApiOperation({
        summary: 'Compare financial data across periods',
        description: 'Returns comparison data for multiple time periods (max 4 periods)'
    })
    @ApiQuery({
        name: 'periods',
        type: [String],
        description: 'Array of periods to compare (format: YYYY-MM for month, YYYY for year)',
        required: true
    })
    @ApiQuery({
        name: 'periodType',
        enum: ['month', 'year'],
        description: 'Type of period comparison',
        required: true
    })
    @ApiResponse({
        status: 200,
        description: 'Comparison data retrieved successfully',
        type: ComparisonDto,
        schema: {
            example: {
                status: 'success',
                statusCode: 200,
                message: 'Comparison data retrieved successfully',
                data: {
                    periods: [
                        {
                            period: '2024-01',
                            income: 3000000,
                            expense: 2500000,
                            balance: 500000,
                            transactionCount: 45
                        },
                        {
                            period: '2024-02',
                            income: 3200000,
                            expense: 2400000,
                            balance: 800000,
                            transactionCount: 52
                        }
                    ],
                    bestPeriod: '2024-02',
                    worstPeriod: '2024-01'
                },
                meta: {},
                timestamp: '2024-12-31T10:00:00.000Z'
            }
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid query parameters or too many periods (max 4)'
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - Invalid or missing JWT token'
    })
    async getComparison(
        @Query(new ValidationPipe({
            transform: true,
            whitelist: true,
            forbidNonWhitelisted: true,
            validationError: { target: false, value: false }
        })) queryDto: ComparisonQueryDto,
        @CurrentUser() user: IUser
    ) {
        const data = await this.statisticsService.getComparison(user.userId, queryDto);
        return {
            data,
            message: 'Comparison data retrieved successfully'
        };
    }
}
