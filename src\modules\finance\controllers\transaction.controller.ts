import {
    Controller,
    Get,
    Post,
    Body,
    Patch,
    Param,
    Delete,
    Query,
    UseInterceptors,
    UploadedFile,
    ParseUUIDPipe,
    ValidationPipe,
    HttpStatus,
    HttpCode,
    BadRequestException,
    UseFilters
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { 
    ApiTags, 
    ApiBearerAuth, 
    ApiConsumes, 
    ApiOperation, 
    ApiResponse,
    ApiParam,
    ApiQuery
} from '@nestjs/swagger';
import { TransactionService } from '../services/transaction.service';
import { 
    CreateTransactionDto, 
    UpdateTransactionDto, 
    TransactionQueryDto,
    DateRangeQueryDto,
    BudgetStatisticsQueryDto
} from '../dto/transaction.dto';
import { CurrentUser } from '@base/api/decorators';
import { IUser } from '@modules/users/interfaces';
import { multerMemoryOptions } from '@modules/upload/configs/multer.config';
import { TransactionResponseInterceptor } from '../interceptors/transaction-response.interceptor';
import { TransactionExceptionFilter } from '../filters/transaction-exception.filter';

@ApiTags('Transactions')
@ApiBearerAuth()
@Controller('transactions')
@UseInterceptors(TransactionResponseInterceptor)
@UseFilters(TransactionExceptionFilter)
export class TransactionController {
    constructor(private readonly transactionService: TransactionService) {}

    @Post()
    @HttpCode(HttpStatus.CREATED)
    @UseInterceptors(FileInterceptor('image', multerMemoryOptions))
    @ApiConsumes('multipart/form-data')
    @ApiOperation({ summary: 'Create a new transaction' })
    @ApiResponse({ status: 201, description: 'Transaction created successfully' })
    @ApiResponse({ status: 400, description: 'Invalid input data' })
    @ApiResponse({ status: 404, description: 'Category not found' })
    async create(
        @Body(new ValidationPipe({ 
            transform: true, 
            whitelist: true, 
            forbidNonWhitelisted: true,
            validationError: { target: false, value: false }
        })) createTransactionDto: CreateTransactionDto,
        @CurrentUser() user: IUser,
        @UploadedFile() file?: Express.Multer.File
    ) {
        return await this.transactionService.create(createTransactionDto, user.userId);
    }

    @Get()
    @ApiOperation({ summary: 'Get all transactions with pagination and filtering' })
    @ApiResponse({ status: 200, description: 'Transactions retrieved successfully' })
    @ApiResponse({ status: 400, description: 'Invalid query parameters' })
    async findAll(
        @Query(new ValidationPipe({ 
            transform: true, 
            whitelist: true,
            forbidNonWhitelisted: true,
            validationError: { target: false, value: false }
        })) query: TransactionQueryDto,
        @CurrentUser() user: IUser
    ) {
        return await this.transactionService.findAll(query, user.userId);
    }

    @Get('statistics/overview')
    @ApiOperation({ summary: 'Get transaction statistics for a date range' })
    @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
    @ApiResponse({ status: 400, description: 'Invalid date range' })
    @ApiQuery({ 
        name: 'start_date', 
        example: '2024-03-01T00:00:00.000Z', 
        description: 'Start date for statistics (ISO 8601 format)',
        required: true
    })
    @ApiQuery({ 
        name: 'end_date', 
        example: '2024-03-31T23:59:59.999Z', 
        description: 'End date for statistics (ISO 8601 format)',
        required: true
    })
    async getStatistics(
        @Query('start_date') startDate: string,
        @Query('end_date') endDate: string,
        @CurrentUser() user: IUser
    ) {
        if (!startDate || !endDate) {
            throw new BadRequestException('Both start_date and end_date are required');
        }

        const start = new Date(startDate);
        const end = new Date(endDate);
        
        if (isNaN(start.getTime()) || isNaN(end.getTime())) {
            throw new BadRequestException('Invalid date format. Use ISO 8601 format (YYYY-MM-DDTHH:mm:ss.sssZ)');
        }

        return await this.transactionService.getStatistics(user.userId, start, end);
    }

    @Get('budget-statistics/:month/:year')
    @ApiOperation({ summary: 'Get budget statistics for a specific month and year' })
    @ApiResponse({ status: 200, description: 'Budget statistics retrieved successfully' })
    @ApiResponse({ status: 400, description: 'Invalid month or year' })
    @ApiParam({ name: 'month', example: 3, description: 'Month (1-12)' })
    @ApiParam({ name: 'year', example: 2024, description: 'Year (2000-2100)' })
    async getBudgetStatistics(
        @Param('month', new ValidationPipe({ transform: true })) month: number,
        @Param('year', new ValidationPipe({ transform: true })) year: number,
        @CurrentUser() user: IUser
    ) {
        // Additional validation
        if (!Number.isInteger(month) || month < 1 || month > 12) {
            throw new BadRequestException('Month must be an integer between 1 and 12');
        }
        
        if (!Number.isInteger(year) || year < 2000 || year > 2100) {
            throw new BadRequestException('Year must be an integer between 2000 and 2100');
        }

        return await this.transactionService.getBudgetStatistics(user.userId, month, year);
    }

    @Get(':id')
    @ApiOperation({ summary: 'Get a transaction by ID' })
    @ApiResponse({ status: 200, description: 'Transaction retrieved successfully' })
    @ApiResponse({ status: 404, description: 'Transaction not found' })
    @ApiParam({ name: 'id', description: 'Transaction UUID' })
    async findOne(
        @Param('id', ParseUUIDPipe) id: string,
        @CurrentUser() user: IUser
    ) {
        return await this.transactionService.findOne(id, user.userId);
    }

    @Patch(':id')
    @UseInterceptors(FileInterceptor('image', multerMemoryOptions))
    @ApiConsumes('multipart/form-data')
    @ApiOperation({ summary: 'Update a transaction' })
    @ApiResponse({ status: 200, description: 'Transaction updated successfully' })
    @ApiResponse({ status: 400, description: 'Invalid input data' })
    @ApiResponse({ status: 404, description: 'Transaction not found' })
    @ApiParam({ name: 'id', description: 'Transaction UUID' })
    async update(
        @Param('id', ParseUUIDPipe) id: string,
        @Body(new ValidationPipe({ 
            transform: true, 
            whitelist: true,
            forbidNonWhitelisted: true,
            validationError: { target: false, value: false }
        })) updateTransactionDto: UpdateTransactionDto,
        @CurrentUser() user: IUser,
        @UploadedFile() file?: Express.Multer.File
    ) {
        return await this.transactionService.update(id, updateTransactionDto, user.userId);
    }

    @Delete(':id')
    @HttpCode(HttpStatus.NO_CONTENT)
    @ApiOperation({ summary: 'Delete a transaction' })
    @ApiResponse({ status: 204, description: 'Transaction deleted successfully' })
    @ApiResponse({ status: 404, description: 'Transaction not found' })
    @ApiParam({ name: 'id', description: 'Transaction UUID' })
    async remove(
        @Param('id', ParseUUIDPipe) id: string,
        @CurrentUser() user: IUser
    ) {
        await this.transactionService.remove(id, user.userId);
    }
} 