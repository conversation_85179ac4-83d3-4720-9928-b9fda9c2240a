import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from '../controllers/auth.controller';
import { AuthService } from '../services/auth.service';
import { GoogleStrategy } from '../google/google.stategy';
import { JwtService } from '@nestjs/jwt';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from '@modules/users/entity';
import { Repository } from 'typeorm';
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { UserProvide, UserRole, UserStatus } from '@modules/users/enums';

describe('Google OAuth2 Implementation', () => {
  let authController: AuthController;
  let authService: AuthService;
  let googleStrategy: GoogleStrategy;
  let userRepository: Repository<User>;
  let jwtService: JwtService;

  const mockUserRepository = {
    findOne: jest.fn(),
    create: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
  };

  const mockJwtService = {
    sign: jest.fn(),
  };

  const mockMailService = {
    sendMail: jest.fn(),
  };

  const mockOtpService = {
    generateOtp: jest.fn(),
    verifyOtp: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        AuthService,
        GoogleStrategy,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: 'MailService',
          useValue: mockMailService,
        },
        {
          provide: 'OtpService',
          useValue: mockOtpService,
        },
      ],
    }).compile();

    authController = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(AuthService);
    googleStrategy = module.get<GoogleStrategy>(GoogleStrategy);
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    jwtService = module.get<JwtService>(JwtService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('GoogleStrategy', () => {
    describe('validate', () => {
      const mockGoogleProfile = {
        id: '123456789',
        displayName: 'John Doe',
        name: {
          givenName: 'John',
          familyName: 'Doe',
        },
        emails: [
          {
            value: '<EMAIL>',
            verified: true,
          },
        ],
        photos: [
          {
            value: 'https://example.com/photo.jpg',
          },
        ],
        provider: 'google',
      };

      const mockRequest = {
        headers: {
          'user-agent': 'test-agent',
        },
        ip: '127.0.0.1',
      };

      it('should create new user when user does not exist', async () => {
        const mockUser = {
          id: 'user-id',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe',
          googleId: '123456789',
          provide: UserProvide.GOOGLE,
          status: UserStatus.ACTIVE,
          role: UserRole.USER,
        };

        mockUserRepository.findOne.mockResolvedValue(null);
        mockUserRepository.create.mockReturnValue(mockUser);
        mockUserRepository.save.mockResolvedValue(mockUser);

        const done = jest.fn();

        await googleStrategy.validate(
          mockRequest,
          'access-token',
          'refresh-token',
          mockGoogleProfile,
          done
        );

        expect(mockUserRepository.findOne).toHaveBeenCalledWith({
          where: [
            { email: '<EMAIL>', provide: UserProvide.GOOGLE },
            { email: '<EMAIL>', provide: UserProvide.LOCAL },
          ],
        });

        expect(mockUserRepository.create).toHaveBeenCalledWith(
          expect.objectContaining({
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            googleId: '123456789',
            provide: UserProvide.GOOGLE,
            status: UserStatus.ACTIVE,
            role: UserRole.USER,
          })
        );

        expect(done).toHaveBeenCalledWith(null, expect.objectContaining({
          ...mockUser,
          oauthTokens: {
            accessToken: 'access-token',
            refreshToken: 'refresh-token',
            provider: 'google',
          },
        }));
      });

      it('should return existing user when user exists', async () => {
        const existingUser = {
          id: 'existing-user-id',
          email: '<EMAIL>',
          provide: UserProvide.GOOGLE,
        };

        mockUserRepository.findOne.mockResolvedValue(existingUser);

        const done = jest.fn();

        await googleStrategy.validate(
          mockRequest,
          'access-token',
          'refresh-token',
          mockGoogleProfile,
          done
        );

        expect(mockUserRepository.save).not.toHaveBeenCalled();
        expect(done).toHaveBeenCalledWith(null, expect.objectContaining({
          ...existingUser,
          oauthTokens: {
            accessToken: 'access-token',
            refreshToken: 'refresh-token',
            provider: 'google',
          },
        }));
      });

      it('should handle invalid profile data', async () => {
        const invalidProfile = {
          id: '123',
          emails: [], // No emails
        };

        const done = jest.fn();

        await googleStrategy.validate(
          mockRequest,
          'access-token',
          'refresh-token',
          invalidProfile as any,
          done
        );

        expect(done).toHaveBeenCalledWith(
          expect.any(BadRequestException),
          null
        );
      });
    });
  });

  describe('AuthService', () => {
    describe('googleLogin', () => {
      const mockUser = {
        id: 'user-id',
        email: '<EMAIL>',
        username: 'johndoe',
        role: UserRole.USER,
      };

      const mockRequest = {
        user: mockUser,
        headers: {
          'user-agent': 'test-agent',
        },
        ip: '127.0.0.1',
      };

      it('should generate tokens and return user data', async () => {
        mockJwtService.sign
          .mockReturnValueOnce('access-token')
          .mockReturnValueOnce('refresh-token');

        mockUserRepository.update.mockResolvedValue({ affected: 1 });

        const result = await authService.googleLogin(mockRequest);

        expect(result.data).toEqual(
          expect.objectContaining({
            user: expect.objectContaining({
              id: 'user-id',
              email: '<EMAIL>',
              username: 'johndoe',
            }),
            accessToken: 'access-token',
            refreshToken: 'refresh-token',
            tokenType: 'Bearer',
            provider: 'google',
          })
        );

        expect(mockUserRepository.update).toHaveBeenCalledWith(
          'user-id',
          expect.objectContaining({
            refreshToken: expect.any(String),
          })
        );
      });

      it('should throw error when no user in request', async () => {
        const requestWithoutUser = {
          headers: {},
          ip: '127.0.0.1',
        };

        await expect(authService.googleLogin(requestWithoutUser))
          .rejects
          .toThrow(BadRequestException);
      });

      it('should validate user data', async () => {
        const invalidUserRequest = {
          user: {
            id: 'user-id',
            // Missing email and username
          },
          headers: {},
          ip: '127.0.0.1',
        };

        await expect(authService.googleLogin(invalidUserRequest))
          .rejects
          .toThrow(BadRequestException);
      });
    });
  });

  describe('AuthController', () => {
    describe('googleAuth', () => {
      it('should log OAuth initiation', async () => {
        const mockRequest = {
          headers: {
            'user-agent': 'test-agent',
            origin: 'https://example.com',
          },
        };

        const logSpy = jest.spyOn(authController['logger'], 'log');

        await authController.googleAuth(mockRequest as any);

        expect(logSpy).toHaveBeenCalledWith('🔄 Initiating Google OAuth2 flow');
      });
    });

    describe('googleAuthCallback', () => {
      const mockRequest = {
        query: {
          code: 'auth-code',
          state: 'csrf-state',
        },
        user: {
          id: 'user-id',
          email: '<EMAIL>',
          provide: UserProvide.GOOGLE,
        },
        headers: {
          'user-agent': 'test-agent',
        },
        ip: '127.0.0.1',
      };

      const mockResponse = {
        setHeader: jest.fn(),
        redirect: jest.fn(),
      };

      it('should handle successful OAuth callback', async () => {
        const mockAuthResult = {
          data: {
            accessToken: 'access-token',
            refreshToken: 'refresh-token',
            user: {
              id: 'user-id',
              email: '<EMAIL>',
            },
            expiresIn: 900,
            tokenType: 'Bearer',
          },
        };

        jest.spyOn(authService, 'googleLogin').mockResolvedValue(mockAuthResult);

        await authController.googleAuthCallback(
          mockRequest as any,
          mockResponse as any
        );

        expect(mockResponse.redirect).toHaveBeenCalledWith(
          302,
          expect.stringContaining('myapp://oauth-callback')
        );
      });

      it('should handle OAuth error in callback', async () => {
        const errorRequest = {
          ...mockRequest,
          query: {
            error: 'access_denied',
            error_description: 'User denied access',
          },
        };

        await authController.googleAuthCallback(
          errorRequest as any,
          mockResponse as any
        );

        expect(mockResponse.redirect).toHaveBeenCalledWith(
          302,
          expect.stringContaining('error=auth_failed')
        );
      });

      it('should handle missing user in callback', async () => {
        const requestWithoutUser = {
          ...mockRequest,
          user: null,
        };

        await authController.googleAuthCallback(
          requestWithoutUser as any,
          mockResponse as any
        );

        expect(mockResponse.redirect).toHaveBeenCalledWith(
          302,
          expect.stringContaining('error=no_user_data')
        );
      });
    });
  });
});
