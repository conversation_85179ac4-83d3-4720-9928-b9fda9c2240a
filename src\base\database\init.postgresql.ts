
import { Otp } from '@base/otp/otp.entity';
import { config } from '@config';
import { Budget, Category, Goal, Notification, Report, ReportDetail, Transaction, ExchangeRate } from '@modules/finance/entity';
import { User } from '@modules/users/entity';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({
    imports: [
        TypeOrmModule.forRoot({
            type: 'postgres',
            host: config.DB_HOST,
            port: config.DB_PORT,
            username: config.DB_USERNAME,
            password: config.DB_PASSWORD,
            database: config.DB_NAME,
            entities: [User, Category, Goal, Transaction, Budget, Notification, Report, ReportDetail, Otp, ExchangeRate],
            autoLoadEntities: true,
            synchronize: false, // Disabled to prevent schema conflicts during decimal migration
        }),
    ],
})
export class InitPostgresql { }
