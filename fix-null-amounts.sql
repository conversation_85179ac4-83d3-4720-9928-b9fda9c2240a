-- Fix NULL values in amount fields before converting to DECIMAL

-- Update budgets table - set NULL amounts to 0
UPDATE budgets SET amount = 0 WHERE amount IS NULL;
UPDATE budgets SET amount_spent = 0 WHERE amount_spent IS NULL;

-- Update transactions table - set NULL amounts to 0  
UPDATE transactions SET amount = 0 WHERE amount IS NULL;
UPDATE transactions SET original_amount = 0 WHERE original_amount IS NULL;

-- Update goals table - set NULL amounts to 0
UPDATE goals SET target_amount = 0 WHERE target_amount IS NULL;
UPDATE goals SET current_amount = 0 WHERE current_amount IS NULL;

-- Update users table - set NULL initial_balance to 0
UPDATE users SET initial_balance = 0 WHERE initial_balance IS NULL;

-- Now convert to DECIMAL type
-- Update users table
ALTER TABLE users ALTER COLUMN initial_balance TYPE DECIMAL(20,8) USING initial_balance::DECIMAL(20,8);

-- Update transactions table  
ALTER TABLE transactions ALTER COLUMN amount TYPE DECIMAL(20,8) USING amount::DECIMAL(20,8);
ALTER TABLE transactions ALTER COLUMN original_amount TYPE DECIMAL(20,8) USING original_amount::DECIMAL(20,8);

-- Update budgets table
ALTER TABLE budgets ALTER COLUMN amount TYPE DECIMAL(20,8) USING amount::DECIMAL(20,8);
ALTER TABLE budgets ALTER COLUMN amount_spent TYPE DECIMAL(20,8) USING amount_spent::DECIMAL(20,8);

-- Update goals table
ALTER TABLE goals ALTER COLUMN target_amount TYPE DECIMAL(20,8) USING target_amount::DECIMAL(20,8);
ALTER TABLE goals ALTER COLUMN current_amount TYPE DECIMAL(20,8) USING current_amount::DECIMAL(20,8);
