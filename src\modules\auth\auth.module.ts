import { config } from "@config";
import { Module } from "@nestjs/common";
import { JwtModule } from '@nestjs/jwt';
import { AuthController } from "./controllers/auth.controller";
import { AuthService } from "./services/auth.service";
import { TypeOrmModule } from "@nestjs/typeorm";
import { User } from "@modules/users/entity";
import { JwtStrategy } from "./jwt";
import { GoogleStrategy } from "./google/google.stategy";
import { Otp } from "@base/otp/otp.entity";
import { MailService } from "@provider/mail/mail.service";
import { OtpService } from "@base/otp/otp.service";

@Module({
    imports: [
        TypeOrmModule.forFeature([User, Otp]),
        JwtModule.register({
            global: true,
            secret: config.JWT_SECRET
        }),
    ],
    controllers: [AuthController],
    providers: [AuthService, JwtStrategy, GoogleStrategy, MailService, OtpService],
    exports: [AuthService]
}
)

export class AuthModule { }