# 🔍 Debug Steps - Mobile App "Quay suốt" sau Google OAuth2

## 🎯 M<PERSON>c tiêu
Tìm ra lý do tại sao mobile app không redirect về Home sau khi hoàn thành Google OAuth2.

## 📋 Step-by-Step Debug Process

### Step 1: Kiểm tra Backend hoạt động
```bash
# 1. Chạy backend
npm run dev

# 2. Test deep link cơ bản
curl -v http://localhost:8000/api/v1/auth/test-deep-link
# Expected: HTTP 302 redirect to financeapp://auth/callback?test=true&timestamp=...

# 3. Test simulate OAuth success
curl -v http://localhost:8000/api/v1/auth/simulate-oauth-success
# Expected: HTTP 302 redirect to financeapp://auth/callback?token=...&refresh_token=...
```

### Step 2: Test Deep Link trên Mobile Device

#### Android:
```bash
# Test deep link với adb
adb shell am start \
  -W -a android.intent.action.VIEW \
  -d "financeapp://auth/callback?token=test_token&refresh_token=test_refresh&test_mode=true" \
  com.yourapp.package

# Kiểm tra logs
adb logcat | grep -i "financeapp\|deeplink\|oauth"
```

#### iOS:
```bash
# Test deep link với simulator
xcrun simctl openurl booted "financeapp://auth/callback?token=test_token&refresh_token=test_refresh&test_mode=true"

# Kiểm tra logs trong Xcode Console
```

### Step 3: Debug Mobile App Code

#### Thêm extensive logging:
```javascript
// App.js hoặc component chính
import { Linking } from 'react-native';

useEffect(() => {
  console.log('🚀 App started, setting up deep link listeners...');
  
  // Xử lý deep link khi app đang chạy
  const handleUrl = (event) => {
    console.log('🔗 Deep link received while app running:', event.url);
    console.log('🔗 Event object:', JSON.stringify(event));
    handleDeepLink(event.url);
  };

  // Đăng ký listener
  const subscription = Linking.addEventListener('url', handleUrl);
  console.log('✅ Deep link listener registered');

  // Xử lý deep link khi app được mở từ deep link
  Linking.getInitialURL().then((url) => {
    console.log('🔗 Initial URL check:', url);
    if (url) {
      console.log('🔗 App opened with deep link:', url);
      handleDeepLink(url);
    } else {
      console.log('📱 App opened normally (no deep link)');
    }
  }).catch(error => {
    console.error('❌ Error getting initial URL:', error);
  });

  return () => {
    console.log('🧹 Cleaning up deep link listener');
    subscription?.remove();
  };
}, []);

const handleDeepLink = (url) => {
  console.log('🔍 Processing deep link:', url);
  console.log('🔍 URL type:', typeof url);
  console.log('🔍 URL length:', url?.length);
  
  try {
    if (!url || typeof url !== 'string') {
      console.error('❌ Invalid URL:', url);
      return;
    }

    if (url.startsWith('financeapp://auth/callback')) {
      console.log('✅ OAuth callback deep link detected');
      
      const urlParts = url.split('?');
      console.log('🔍 URL parts:', urlParts);
      
      if (urlParts.length > 1) {
        const queryString = urlParts[1];
        console.log('🔍 Query string:', queryString);
        
        const params = new URLSearchParams(queryString);
        console.log('🔍 Parsed params:', Object.fromEntries(params));
        
        const token = params.get('token');
        const refreshToken = params.get('refresh_token');
        const userEmail = params.get('user_email');
        const testMode = params.get('test_mode');
        const error = params.get('error');
        
        console.log('🎯 Extracted values:', {
          token: token ? `${token.substring(0, 20)}...` : null,
          refreshToken: refreshToken ? `${refreshToken.substring(0, 20)}...` : null,
          userEmail,
          testMode,
          error
        });
        
        if (token && refreshToken) {
          console.log('✅ Valid tokens found, processing login...');
          saveTokensAndNavigate(token, refreshToken, userEmail, testMode === 'true');
        } else if (error) {
          console.log('❌ OAuth error detected:', error);
          const message = params.get('message') || 'Authentication failed';
          console.log('❌ Error message:', message);
          Alert.alert('Đăng nhập thất bại', decodeURIComponent(message));
        } else {
          console.error('❌ No valid tokens or error found in deep link');
          console.error('❌ Available params:', Object.fromEntries(params));
        }
      } else {
        console.error('❌ No query parameters in deep link');
      }
    } else {
      console.log('ℹ️ Non-OAuth deep link:', url);
    }
  } catch (error) {
    console.error('❌ Error processing deep link:', error);
    console.error('❌ Error stack:', error.stack);
  }
};

const saveTokensAndNavigate = async (token, refreshToken, userEmail, isTestMode = false) => {
  try {
    console.log('💾 Saving tokens...', { userEmail, isTestMode });
    
    await AsyncStorage.setItem('accessToken', token);
    console.log('✅ Access token saved');
    
    await AsyncStorage.setItem('refreshToken', refreshToken);
    console.log('✅ Refresh token saved');
    
    if (userEmail) {
      await AsyncStorage.setItem('userEmail', userEmail);
      console.log('✅ User email saved');
    }
    
    if (isTestMode) {
      await AsyncStorage.setItem('testMode', 'true');
      console.log('✅ Test mode flag saved');
    }
    
    console.log('🏠 Navigating to Home...');
    
    // Test navigation
    if (navigation) {
      console.log('🧭 Navigation object available');
      console.log('🧭 Current route:', navigation.getCurrentRoute?.()?.name);
      
      navigation.reset({
        index: 0,
        routes: [{ name: 'Home' }],
      });
      
      console.log('✅ Navigation reset completed');
    } else {
      console.error('❌ Navigation object not available');
    }
    
  } catch (error) {
    console.error('❌ Error saving tokens or navigating:', error);
    console.error('❌ Error stack:', error.stack);
    Alert.alert('Lỗi', 'Không thể lưu thông tin đăng nhập: ' + error.message);
  }
};
```

### Step 4: Test từng bước

#### Test 1: Deep Link cơ bản
```javascript
// Thêm button test trong app
const testDeepLink = () => {
  console.log('🧪 Testing deep link manually...');
  const testUrl = 'financeapp://auth/callback?test=true&timestamp=' + Date.now();
  console.log('🧪 Test URL:', testUrl);
  
  Linking.openURL(testUrl).then(() => {
    console.log('✅ Deep link opened successfully');
  }).catch(error => {
    console.error('❌ Error opening deep link:', error);
  });
};

// Thêm button trong UI
<Button title="Test Deep Link" onPress={testDeepLink} />
```

#### Test 2: Navigation
```javascript
// Test navigation trực tiếp
const testNavigation = () => {
  console.log('🧪 Testing navigation...');
  console.log('🧭 Current route:', navigation.getCurrentRoute?.()?.name);
  
  try {
    navigation.reset({
      index: 0,
      routes: [{ name: 'Home' }],
    });
    console.log('✅ Navigation test completed');
  } catch (error) {
    console.error('❌ Navigation test failed:', error);
  }
};

<Button title="Test Navigation" onPress={testNavigation} />
```

#### Test 3: Token validation
```javascript
// Test token sau khi nhận
const testToken = async (token) => {
  console.log('🧪 Testing token validity...');
  
  try {
    const response = await fetch('http://your-api.com/api/v1/users/profile', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('🔍 Token test response status:', response.status);
    
    if (response.ok) {
      const user = await response.json();
      console.log('✅ Token valid, user data:', user);
      return true;
    } else {
      const errorText = await response.text();
      console.log('❌ Token invalid, error:', errorText);
      return false;
    }
  } catch (error) {
    console.error('❌ Token test error:', error);
    return false;
  }
};
```

### Step 5: Common Issues & Solutions

#### Issue 1: Deep link không được trigger
```javascript
// Kiểm tra app có đăng ký deep link scheme không
Linking.canOpenURL('financeapp://test').then(supported => {
  console.log('🔍 Deep link scheme supported:', supported);
  if (!supported) {
    console.error('❌ Deep link scheme not supported!');
    Alert.alert('Lỗi', 'App chưa được cấu hình deep link đúng cách');
  }
});
```

#### Issue 2: Navigation không hoạt động
```javascript
// Kiểm tra navigation context
const navigation = useNavigation();

useEffect(() => {
  console.log('🧭 Navigation object:', !!navigation);
  console.log('🧭 Navigation methods:', Object.keys(navigation || {}));
  
  if (navigation) {
    const unsubscribe = navigation.addListener('state', (e) => {
      console.log('🧭 Navigation state changed:', e.data.state.routeNames);
      console.log('🧭 Current route index:', e.data.state.index);
    });
    
    return unsubscribe;
  }
}, [navigation]);
```

#### Issue 3: AsyncStorage issues
```javascript
// Test AsyncStorage
const testAsyncStorage = async () => {
  try {
    await AsyncStorage.setItem('test_key', 'test_value');
    const value = await AsyncStorage.getItem('test_key');
    console.log('✅ AsyncStorage working:', value === 'test_value');
    await AsyncStorage.removeItem('test_key');
  } catch (error) {
    console.error('❌ AsyncStorage error:', error);
  }
};
```

## 🚀 Quick Fixes

### Fix 1: Fallback mechanism
```javascript
// Thêm timeout cho OAuth flow
useEffect(() => {
  let oauthTimeout;
  
  const startOAuthTimeout = () => {
    oauthTimeout = setTimeout(() => {
      console.log('⏰ OAuth timeout reached');
      Alert.alert(
        'Đăng nhập chậm?', 
        'Có vẻ như quá trình đăng nhập đang gặp vấn đề. Bạn muốn thử lại?',
        [
          { text: 'Thử lại', onPress: () => startGoogleLogin() },
          { text: 'Hủy', style: 'cancel' }
        ]
      );
    }, 30000); // 30 seconds
  };
  
  return () => {
    if (oauthTimeout) clearTimeout(oauthTimeout);
  };
}, []);
```

### Fix 2: Manual redirect
```javascript
// Thêm button manual redirect cho debug
const manualRedirect = () => {
  console.log('🔧 Manual redirect to Home');
  navigation.reset({
    index: 0,
    routes: [{ name: 'Home' }],
  });
};

// Hiển thị trong UI khi debug
{__DEV__ && (
  <Button title="Manual Redirect to Home" onPress={manualRedirect} />
)}
```

## 📊 Monitoring

Thêm analytics để track OAuth flow:
```javascript
const trackEvent = (event, data = {}) => {
  console.log(`📊 Event: ${event}`, data);
  // Gửi lên analytics service nếu có
};

// Usage
trackEvent('oauth_started');
trackEvent('deep_link_received', { url });
trackEvent('tokens_saved', { userEmail });
trackEvent('navigation_attempted');
trackEvent('navigation_completed');
```
