import { Injectable, NotFoundException, BadRequestException, Inject, Scope } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';
import { Goal } from '../entity';
import { CreateGoalDto, UpdateGoalDto, GoalQueryDto } from '../dto/goal.dto';
import { GoalStatus } from '../enums';
import { IUser } from '@modules/users/interfaces';

@Injectable({ scope: Scope.REQUEST })
export class GoalService {
    constructor(
        @InjectRepository(Goal)
        private goalRepository: Repository<Goal>,
        @Inject(REQUEST) private request: Request
    ) {}

    private get currentUser(): IUser {
        return this.request.user as IUser;
    }

    private get userId(): string {
        return this.currentUser.userId;
    }

    async create(createGoalDto: CreateGoalDto): Promise<{ data: Goal }> {
        // Check for duplicate goals
        const existingGoal = await this.goalRepository.findOne({
            where: {
                user_id: this.userId,
                name: createGoalDto.name,
                target_date: createGoalDto.target_date
            }
        });

        if (existingGoal) {
            throw new BadRequestException('A goal with the same name already exists for this time period');
        }

        // Determine initial status based on start_date
        const now = new Date();
        const startDate = new Date(createGoalDto.start_date);
        const initialStatus = startDate > now ? GoalStatus.PENDING : GoalStatus.ACTIVE;

        const goal = this.goalRepository.create({
            ...createGoalDto,
            user_id: this.userId,
            status: initialStatus
        });

        const savedGoal = await this.goalRepository.save(goal);
        return {
            data: savedGoal
        };
    }

    async findAll(query: GoalQueryDto) {
        const { status, page = 1, limit = 10 } = query;
        const skip = (page - 1) * limit;

        const queryBuilder = this.goalRepository.createQueryBuilder('goal')
            .where('goal.user_id = :userId', { userId: this.userId });

        if (status) {
            queryBuilder.andWhere('goal.status = :status', { status });
        }

        const [goals, total] = await queryBuilder
            .skip(skip)
            .take(limit)
            .getManyAndCount();

        return {
            data: goals,
            total,
            page,
            limit
        };
    }

    async findOne(id: string): Promise<{ data: Goal }> {
        const goal = await this.goalRepository.findOne({
            where: { id, user_id: this.userId }
        });

        if (!goal) {
            throw new NotFoundException('Goal not found');
        }

        return {
            data: goal
        };
    }

    async update(id: string, updateGoalDto: UpdateGoalDto): Promise<{ data: Goal }> {
        const goal = await this.findOne(id);

        // If updating name, check for duplicates
        if (updateGoalDto.name) {
            const duplicate = await this.goalRepository.findOne({
                where: {
                    user_id: this.userId,
                    name: updateGoalDto.name,
                    target_date: goal.data.target_date,
                    id: Not(id)
                }
            });

            if (duplicate) {
                throw new BadRequestException('A goal with the same name already exists for this time period');
            }
        }

        // Check if goal can be completed
        const finalCurrentAmount = updateGoalDto.current_amount !== undefined ? updateGoalDto.current_amount : goal.data.current_amount;
        const finalTargetAmount = updateGoalDto.target_amount !== undefined ? updateGoalDto.target_amount : goal.data.target_amount;
        if (updateGoalDto.status === GoalStatus.COMPLETED && finalCurrentAmount < finalTargetAmount) {
            throw new BadRequestException('Cannot complete goal: current amount has not reached target amount');
        }

        Object.assign(goal.data, updateGoalDto);
        const updatedGoal = await this.goalRepository.save(goal.data);
        return {
            data: updatedGoal
        };
    }

    async remove(id: string): Promise<{ data: { success: boolean } }> {
        const goal = await this.findOne(id);
        await this.goalRepository.remove(goal.data);
        return {
            data: { success: true }
        };
    }
}