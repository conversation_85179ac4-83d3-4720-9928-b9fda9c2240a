import {
    PrimaryGeneratedColumn,
    CreateDateColumn,
    UpdateDateColumn,
    DeleteDateColumn,
    Column,
    BeforeInsert,
    BeforeUpdate,
    BeforeRemove,
    BaseEntity as TypeOrmBaseEntity,
} from 'typeorm';

/**
 * <PERSON>ớ<PERSON> cơ sở nâng cao cho các entity, bao gồm tracking user t<PERSON><PERSON>, cậ<PERSON> nhật, xóa.
 */
export class BaseEntity {
    @PrimaryGeneratedColumn('uuid')
    id: string;

    @CreateDateColumn({ type: 'timestamp' })
    created_at?: Date;

    @UpdateDateColumn({ type: 'timestamp' })
    updated_at?: Date;

    @DeleteDateColumn({ type: 'timestamp', nullable: true })
    deleted_at?: Date;
}
