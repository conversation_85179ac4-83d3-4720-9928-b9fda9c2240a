import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Budget, Category, Goal, Notification, Report, ReportDetail, Transaction, ExchangeRate } from "./entity";
import { User } from "@modules/users/entity";
import { GoalController } from "./controllers/goal.controller";
import { GoalService } from "./services/goal.service";
import { CategoryController } from "./controllers/category.controller";
import { CategoryService } from "./services/category.service";
import { BudgetController } from "./controllers/budget.controller";
import { BudgetService } from "./services/budget.service";
import { TransactionController } from "./controllers/transaction.controller";
import { TransactionService } from "./services/transaction.service";
import { StatisticsController } from "./controllers/statistics.controller";
import { StatisticsService } from "./services/statistics.service";
import { CurrencyService } from "./services/currency.service";
import { I18nService } from "./services/i18n.service";
import { I18nModule } from "./modules/i18n.module";
import { ScheduleModule } from '@nestjs/schedule';

@Module({
    imports: [
        TypeOrmModule.forFeature([Category, Goal, Transaction, Budget, Notification, Report, ReportDetail, User, ExchangeRate]),
        I18nModule,
        ScheduleModule.forRoot()
    ],
    controllers: [GoalController, CategoryController, BudgetController, TransactionController, StatisticsController],
    providers: [GoalService, CategoryService, BudgetService, TransactionService, StatisticsService, CurrencyService, I18nService],
    exports: [GoalService, CategoryService, BudgetService, TransactionService, StatisticsService, CurrencyService, I18nService]
})
export class FinanceModule { }