# 🧪 Test Google OAuth2 Flow

## 📋 Checklist kiểm tra

### ✅ <PERSON><PERSON><PERSON> hình cơ bản
- [ ] GOOGLE_CLIENT_ID đã được set trong .env
- [ ] GOOGLE_CLIENT_SECRET đã được set trong .env  
- [ ] GOOGLE_CALLBACK_URL đúng format và khớp với Google Console
- [ ] GOOGLE_DEEP_LINK đã được cấu hình

### ✅ Google Console Setup
- [ ] OAuth 2.0 Client đã được tạo
- [ ] Authorized redirect URIs đã được thêm
- [ ] Google+ API đã được enable
- [ ] Test users đã được thêm (nếu app chưa publish)

### ✅ Backend Endpoints
- [ ] `GET /api/v1/auth/google` redirect đến Google
- [ ] `GET /api/v1/auth/google/callback` xử lý callback
- [ ] `GET /api/v1/debug/google-oauth` hiển thị config

## 🔧 Test Commands

### 1. <PERSON><PERSON><PERSON> tra cấu hình
```bash
curl http://localhost:8000/api/v1/debug/google-oauth
```

### 2. Test OAuth2 flow (Manual)
```bash
# Mở browser và truy cập:
http://localhost:8000/api/v1/auth/google

# Hoặc với production:
https://vuquangduy.io.vn/api/v1/auth/google
```

### 3. Test Deep Link (Mobile)
```javascript
// Test deep link trong mobile app
const testDeepLink = 'financeapp://auth/callback?token=test_token&refresh_token=test_refresh';
Linking.openURL(testDeepLink);
```

## 📱 Mobile App Test

### Android Deep Link Test
```bash
# Test deep link với adb
adb shell am start \
  -W -a android.intent.action.VIEW \
  -d "financeapp://auth/callback?token=test_token" \
  com.yourapp.package
```

### iOS Deep Link Test
```bash
# Test deep link với simulator
xcrun simctl openurl booted "financeapp://auth/callback?token=test_token"
```

## 🐛 Debug Steps

### 1. Kiểm tra logs
```bash
# Xem logs khi chạy OAuth2 flow
npm run dev

# Logs sẽ hiển thị:
# 🔄 Initiating Google OAuth2 flow
# 📥 Google OAuth2 callback received  
# ✅ Google OAuth2 successful for user: <EMAIL>
# 🔗 Redirecting to deep link
```

### 2. Test từng bước

#### Bước 1: Initiate OAuth2
```bash
curl -v http://localhost:8000/api/v1/auth/google
# Expected: HTTP 302 redirect to accounts.google.com
```

#### Bước 2: Manual callback test
```bash
# Thay thế YOUR_CODE bằng code thực từ Google
curl -v "http://localhost:8000/api/v1/auth/google/callback?code=YOUR_CODE&state=YOUR_STATE"
# Expected: HTTP 302 redirect to financeapp://auth/callback?token=...
```

### 3. Kiểm tra database
```sql
-- Kiểm tra user được tạo sau OAuth2
SELECT id, email, firstName, lastName, provide, googleId, avatar 
FROM users 
WHERE provide = 'google' 
ORDER BY created_at DESC 
LIMIT 5;
```

## 🚨 Common Issues & Solutions

### Issue 1: "redirect_uri_mismatch"
```bash
# Kiểm tra callback URL
echo $GOOGLE_CALLBACK_URL

# So sánh với Google Console
# Development: https://your-ngrok.ngrok-free.app/api/v1/auth/google/callback
# Production: https://vuquangduy.io.vn/api/v1/auth/google/callback
```

### Issue 2: "invalid_client"
```bash
# Kiểm tra credentials
echo $GOOGLE_CLIENT_ID
echo $GOOGLE_CLIENT_SECRET

# Đảm bảo không có khoảng trắng thừa
```

### Issue 3: Deep link không hoạt động
```javascript
// Kiểm tra mobile app đã register scheme
// Android: android:scheme="financeapp" trong AndroidManifest.xml
// iOS: CFBundleURLSchemes trong Info.plist
```

### Issue 4: CORS errors
```bash
# Kiểm tra domain trong Google Console
# Thêm domain vào Authorized JavaScript origins
```

## 📊 Success Criteria

### ✅ OAuth2 flow thành công khi:
1. User click vào Google login button
2. Browser mở và redirect đến Google
3. User đăng nhập Google thành công
4. Google redirect về backend callback
5. Backend tạo JWT token
6. Backend redirect về mobile app với token
7. Mobile app nhận được token và chuyển màn hình

### ✅ Backend logs hiển thị:
```
🔄 Initiating Google OAuth2 flow
🔍 Validating Google OAuth2 profile
👤 Processing user with email: <EMAIL>
✅ Existing user found: uuid-here
🎉 Google OAuth2 validation successful for user: <EMAIL>
📥 Google OAuth2 callback received
✅ Google OAuth2 successful for user: <EMAIL>
🔗 Redirecting to deep link: financeapp://auth/callback?token=***&refresh_token=***
```

### ✅ Mobile app nhận được:
```
financeapp://auth/callback?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...&refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 🔄 End-to-End Test Script

```javascript
// Tạo script test tự động (pseudo-code)
async function testGoogleOAuth() {
  // 1. Kiểm tra config
  const config = await fetch('/api/v1/debug/google-oauth');
  assert(config.clientId !== 'NOT_SET');
  
  // 2. Test initiate OAuth2
  const response = await fetch('/api/v1/auth/google', { redirect: 'manual' });
  assert(response.status === 302);
  assert(response.headers.location.includes('accounts.google.com'));
  
  // 3. Test callback (cần manual intervention)
  console.log('Manual step: Complete Google OAuth2 and check deep link');
  
  // 4. Verify token
  const token = 'token-from-deep-link';
  const userProfile = await fetch('/api/v1/users/profile', {
    headers: { Authorization: `Bearer ${token}` }
  });
  assert(userProfile.status === 200);
  
  console.log('✅ Google OAuth2 test passed!');
}
```
