import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { I18nService } from '@modules/finance/services/i18n.service';
import { DEFAULT_LANGUAGE } from '@modules/finance/enums/language.enum';

// Extend Request interface to include localization properties
declare global {
    namespace Express {
        interface Request {
            language?: string;
            currency?: string;
        }
    }
}

@Injectable()
export class LocalizationMiddleware implements NestMiddleware {
    constructor(private readonly i18nService: I18nService) { }

    use(req: Request, res: Response, next: NextFunction) {
        // Detect language from various sources
        req.language = this.detectLanguage(req);

        // Detect currency from user preferences or default
        req.currency = this.detectCurrency(req);

        next();
    }

    private detectLanguage(req: Request): string {
        // Priority order:
        // 1. Query parameter (?lang=vi)
        // 2. User preference (if authenticated)
        // 3. Accept-Language header
        // 4. Default language

        // 1. Query parameter
        if (req.query.lang && typeof req.query.lang === 'string') {
            const lang = req.query.lang.toLowerCase();
            if (this.i18nService.isLanguageSupported(lang)) {
                return lang;
            }
        }

        // 2. User preference (if authenticated)
        if (req.user && (req.user as any).preferred_language) {
            const lang = (req.user as any).preferred_language.toLowerCase();
            if (this.i18nService.isLanguageSupported(lang)) {
                return lang;
            }
        }

        // 3. Accept-Language header
        const acceptLanguage = req.headers['accept-language'];
        if (acceptLanguage) {
            const detectedLang = this.i18nService.detectLanguage(acceptLanguage);
            if (this.i18nService.isLanguageSupported(detectedLang)) {
                return detectedLang;
            }
        }

        // 4. Default language
        return DEFAULT_LANGUAGE;
    }

    private detectCurrency(req: Request): string {
        // Priority order:
        // 1. Query parameter (?currency=VND)
        // 2. User preference (if authenticated)
        // 3. Default based on language
        // 4. USD as fallback

        // 1. Query parameter
        if (req.query.currency && typeof req.query.currency === 'string') {
            const currency = req.query.currency.toUpperCase();
            if (this.isCurrencySupported(currency)) {
                return currency;
            }
        }

        // 2. User preference (if authenticated)
        if (req.user && (req.user as any).preferred_currency) {
            const currency = (req.user as any).preferred_currency.toUpperCase();
            if (this.isCurrencySupported(currency)) {
                return currency;
            }
        }

        // 3. Default based on language
        const language = req.language || DEFAULT_LANGUAGE;
        const defaultCurrency = this.getDefaultCurrencyForLanguage(language);
        if (this.isCurrencySupported(defaultCurrency)) {
            return defaultCurrency;
        }

        // 4. USD as fallback
        return 'USD';
    }

    private isCurrencySupported(currency: string): boolean {
        const supportedCurrencies = ['USD', 'VND', 'CNY'];
        return supportedCurrencies.includes(currency);
    }

    private getDefaultCurrencyForLanguage(language: string): string {
        const languageCurrencyMap = {
            'en': 'USD',
            'vi': 'VND',
            'zh': 'CNY'
        };
        return languageCurrencyMap[language] || 'USD';
    }
}
