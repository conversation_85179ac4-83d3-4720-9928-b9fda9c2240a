import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateNumberColumnsToBigint1747969000000 implements MigrationInterface {
    name = 'UpdateNumberColumnsToBigint1747969000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // First, update NULL values to 0 for all amount columns

        // Update goals table - handle NULL values first
        await queryRunner.query(`UPDATE "goals" SET "target_amount" = 0 WHERE "target_amount" IS NULL`);
        await queryRunner.query(`UPDATE "goals" SET "current_amount" = 0 WHERE "current_amount" IS NULL`);
        await queryRunner.query(`ALTER TABLE "goals" ALTER COLUMN "target_amount" TYPE bigint USING "target_amount"::bigint`);
        await queryRunner.query(`ALTER TABLE "goals" ALTER COLUMN "current_amount" TYPE bigint USING "current_amount"::bigint`);
        await queryRunner.query(`ALTER TABLE "goals" ALTER COLUMN "target_amount" SET NOT NULL`);
        await queryRunner.query(`ALTER TABLE "goals" ALTER COLUMN "current_amount" SET NOT NULL`);

        // Update transactions table
        await queryRunner.query(`UPDATE "transactions" SET "amount" = 0 WHERE "amount" IS NULL`);
        await queryRunner.query(`ALTER TABLE "transactions" ALTER COLUMN "amount" TYPE bigint USING "amount"::bigint`);
        await queryRunner.query(`ALTER TABLE "transactions" ALTER COLUMN "amount" SET NOT NULL`);

        // Update budgets table
        await queryRunner.query(`UPDATE "budgets" SET "amount" = 0 WHERE "amount" IS NULL`);
        await queryRunner.query(`ALTER TABLE "budgets" ALTER COLUMN "amount" TYPE bigint USING "amount"::bigint`);
        await queryRunner.query(`ALTER TABLE "budgets" ALTER COLUMN "amount" SET NOT NULL`);

        // Update reports table
        await queryRunner.query(`UPDATE "reports" SET "total_planned" = 0 WHERE "total_planned" IS NULL`);
        await queryRunner.query(`UPDATE "reports" SET "total_actual" = 0 WHERE "total_actual" IS NULL`);
        await queryRunner.query(`UPDATE "reports" SET "total_difference" = 0 WHERE "total_difference" IS NULL`);
        await queryRunner.query(`ALTER TABLE "reports" ALTER COLUMN "total_planned" TYPE bigint USING "total_planned"::bigint`);
        await queryRunner.query(`ALTER TABLE "reports" ALTER COLUMN "total_actual" TYPE bigint USING "total_actual"::bigint`);
        await queryRunner.query(`ALTER TABLE "reports" ALTER COLUMN "total_difference" TYPE bigint USING "total_difference"::bigint`);

        // Update report_details table
        await queryRunner.query(`UPDATE "report_details" SET "planned_amount" = 0 WHERE "planned_amount" IS NULL`);
        await queryRunner.query(`UPDATE "report_details" SET "actual_amount" = 0 WHERE "actual_amount" IS NULL`);
        await queryRunner.query(`UPDATE "report_details" SET "difference_amount" = 0 WHERE "difference_amount" IS NULL`);
        await queryRunner.query(`UPDATE "report_details" SET "completion_percentage" = 0 WHERE "completion_percentage" IS NULL`);
        await queryRunner.query(`ALTER TABLE "report_details" ALTER COLUMN "planned_amount" TYPE bigint USING "planned_amount"::bigint`);
        await queryRunner.query(`ALTER TABLE "report_details" ALTER COLUMN "actual_amount" TYPE bigint USING "actual_amount"::bigint`);
        await queryRunner.query(`ALTER TABLE "report_details" ALTER COLUMN "difference_amount" TYPE bigint USING "difference_amount"::bigint`);
        await queryRunner.query(`ALTER TABLE "report_details" ALTER COLUMN "completion_percentage" TYPE decimal(5,2) USING "completion_percentage"::decimal(5,2)`);

        // Update users table
        await queryRunner.query(`UPDATE "users" SET "initial_balance" = 0 WHERE "initial_balance" IS NULL`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "initial_balance" TYPE bigint USING "initial_balance"::bigint`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert users table
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "initial_balance" TYPE integer`);

        // Revert report_details table
        await queryRunner.query(`ALTER TABLE "report_details" ALTER COLUMN "completion_percentage" TYPE integer`);
        await queryRunner.query(`ALTER TABLE "report_details" ALTER COLUMN "difference_amount" TYPE integer`);
        await queryRunner.query(`ALTER TABLE "report_details" ALTER COLUMN "actual_amount" TYPE integer`);
        await queryRunner.query(`ALTER TABLE "report_details" ALTER COLUMN "planned_amount" TYPE integer`);

        // Revert reports table
        await queryRunner.query(`ALTER TABLE "reports" ALTER COLUMN "total_difference" TYPE integer`);
        await queryRunner.query(`ALTER TABLE "reports" ALTER COLUMN "total_actual" TYPE integer`);
        await queryRunner.query(`ALTER TABLE "reports" ALTER COLUMN "total_planned" TYPE integer`);

        // Revert budgets table
        await queryRunner.query(`ALTER TABLE "budgets" ALTER COLUMN "amount" TYPE integer`);

        // Revert transactions table
        await queryRunner.query(`ALTER TABLE "transactions" ALTER COLUMN "amount" TYPE integer`);

        // Revert goals table
        await queryRunner.query(`ALTER TABLE "goals" ALTER COLUMN "current_amount" TYPE integer`);
        await queryRunner.query(`ALTER TABLE "goals" ALTER COLUMN "target_amount" TYPE integer`);
    }
}
