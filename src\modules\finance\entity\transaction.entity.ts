import {
    <PERSON><PERSON><PERSON>,
    Column,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Join<PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { BaseEntity } from '@base/entity';
import { User } from '@modules/users/entity';
import { Category } from './category.entity';
import { InputMethod, TransactionType } from '../enums';

@Entity({ name: 'transactions' })
export class Transaction extends BaseEntity {
    @ManyToOne(() => User, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'user_id' })
    user: User;

    @Column({ type: 'uuid' })
    user_id: string;

    @ManyToOne(() => Category, { onDelete: 'SET NULL', nullable: true })
    @JoinColumn({ name: 'category_id' })
    category: Category;

    @Column({ type: 'uuid', nullable: true })
    category_id: string;

    @Column({ type: 'decimal', precision: 20, scale: 8, nullable: false, transformer: { to: (value) => value, from: (value) => parseFloat(value) } })
    amount: number;

    @Column({
        type: 'enum',
        enum: TransactionType,
    })
    type: TransactionType;

    @Column()
    transaction_date: Date;

    @Column({
        type: 'enum',
        enum: InputMethod,
        default: InputMethod.MANUAL,
    })
    input_method: InputMethod;

    @Column({ nullable: true })
    description?: string;

    @Column({ nullable: true })
    notes?: string;

    @Column({ nullable: true })
    image_url?: string;

    // Multi-currency fields
    @Column({ length: 3, default: 'USD' })
    original_currency: string;

    @Column({ type: 'decimal', precision: 20, scale: 8, nullable: true, transformer: { to: (value) => value, from: (value) => value ? parseFloat(value) : null } })
    original_amount: number;

    @Column('decimal', { precision: 15, scale: 8, default: 1.0 })
    exchange_rate: number;
}
