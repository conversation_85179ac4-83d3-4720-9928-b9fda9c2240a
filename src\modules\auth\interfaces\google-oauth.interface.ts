export interface GoogleProfile {
  id: string;
  displayName: string;
  name: {
    familyName: string;
    givenName: string;
  };
  emails: Array<{
    value: string;
    verified: boolean;
  }>;
  photos: Array<{
    value: string;
  }>;
  provider: string;
  _raw: string;
  _json: any;
}

export interface GoogleOAuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn?: number;
  tokenType?: string;
}

export interface GoogleUserInfo {
  id: string;
  email: string;
  verified_email: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  locale: string;
}

export interface OAuthCallbackQuery {
  code?: string;
  state?: string;
  error?: string;
  error_description?: string;
  scope?: string;
  authuser?: string;
  prompt?: string;
}

export interface DeepLinkParams {
  accessToken: string;
  refreshToken: string;
  userEmail?: string;
  userId?: string;
  error?: string;
  message?: string;
  timestamp?: number;
}

export interface GoogleOAuthConfig {
  clientID: string;
  clientSecret: string;
  callbackURL: string;
  scope: string[];
}

export interface OAuthValidationResult {
  isValid: boolean;
  user?: any;
  error?: string;
  errorDescription?: string;
}

export interface SecurityContext {
  userAgent?: string;
  ipAddress?: string;
  timestamp: number;
  sessionId?: string;
}

export interface OAuthAuditLog {
  userId?: string;
  email?: string;
  action: 'oauth_initiated' | 'oauth_callback' | 'oauth_success' | 'oauth_error';
  provider: 'google';
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  details?: any;
  error?: string;
}
