import { HttpExceptionFilter } from '@base/api/exception';
import { InitPostgresql } from '@base/database';
import { ConfigModule } from '@config';
import { AuthModule } from '@modules/auth/auth.module';
import { FinanceModule } from '@modules/finance/finance.module';
import { UserModule } from '@modules/users/user.module';
import { DebugModule } from '@modules/debug/debug.module';
import { Module } from '@nestjs/common';

import { APP_FILTER, APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from '@modules/auth/jwt';
import { MailModule } from '@provider/mail/mail.module';
import { Otp } from '@base/otp/otp.entity';
import { OtpModule } from '@base/otp/otp.module';
import { UploadModule } from '@modules/upload/upload.module';
import { ScheduleModule } from '@nestjs/schedule';


@Module({
  imports: [
    // globals
    ConfigModule,
    OtpModule,
    ScheduleModule.forRoot(),

    // DB
    InitPostgresql,
    // InitRedis,

    // Modules
    UserModule,
    AuthModule,
    FinanceModule,
    DebugModule,
    UploadModule,

    // Providers
    MailModule
  ],
  controllers: [],
  providers: [
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter
    }
  ],
})
export class AppModule { }
