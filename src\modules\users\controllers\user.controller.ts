import { Controller, Post, Get, Body, HttpCode, HttpStatus, Patch } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { UserService } from '../services';
import { SetInitialBalanceDto, InitialBalanceResponseDto } from '../dto';
import { UpdateProfileDto } from '../dto/update-profile.dto';
import { UpdateUserPreferencesDto } from '../dto/user-preferences.dto';

@ApiTags('Users')
@ApiBearerAuth()
@Controller('users')
export class UserController {
    constructor(private readonly userService: UserService) { }

    @Post('initial-balance')
    @HttpCode(HttpStatus.OK)
    @ApiOperation({
        summary: 'Set initial balance',
        description: 'Set the initial balance for the current user and mark as initialized'
    })
    @ApiResponse({
        status: 200,
        description: 'Initial balance set successfully',
        type: InitialBalanceResponseDto
    })
    @ApiResponse({
        status: 404,
        description: 'User not found'
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized'
    })
    setInitialBalance(@Body() setInitialBalanceDto: SetInitialBalanceDto) {
        return this.userService.setInitialBalance(setInitialBalanceDto);
    }

    @Get('profile')
    @ApiOperation({
        summary: 'Get user profile',
        description: 'Get the current user profile information'
    })
    @ApiResponse({
        status: 200,
        description: 'User profile retrieved successfully'
    })
    @ApiResponse({
        status: 404,
        description: 'User not found'
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized'
    })
    getProfile() {
        return this.userService.getProfile();
    }

    @Patch('profile')
    async updateProfile(@Body() dto: UpdateProfileDto) {
        return this.userService.updateProfile(dto);
    }

    @Get('preferences')
    @ApiOperation({
        summary: 'Get user preferences',
        description: 'Get user language, currency and timezone preferences'
    })
    @ApiResponse({
        status: 200,
        description: 'User preferences retrieved successfully'
    })
    getPreferences() {
        return this.userService.getPreferences();
    }

    @Patch('preferences')
    @ApiOperation({
        summary: 'Update user preferences',
        description: 'Update user language, currency and timezone preferences'
    })
    @ApiResponse({
        status: 200,
        description: 'User preferences updated successfully'
    })
    updatePreferences(@Body() dto: UpdateUserPreferencesDto) {
        return this.userService.updatePreferences(dto);
    }
}
