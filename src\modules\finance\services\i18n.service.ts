import { Injectable } from '@nestjs/common';
import { I18nService as NestI18nService } from 'nestjs-i18n';
import { Language, DEFAULT_LANGUAGE } from '../enums/language.enum';

@Injectable()
export class I18nService {
    constructor(private readonly i18n: NestI18nService) {}

    /**
     * Translate a key to the specified language
     */
    translate(key: string, lang: string = DEFAULT_LANGUAGE, args?: any): string {
        try {
            return this.i18n.translate(key, { lang, args });
        } catch (error) {
            // Fallback to English if translation fails
            if (lang !== DEFAULT_LANGUAGE) {
                return this.i18n.translate(key, { lang: DEFAULT_LANGUAGE, args });
            }
            return key; // Return key if all else fails
        }
    }

    /**
     * Detect language from request headers
     */
    detectLanguage(acceptLanguage?: string): string {
        if (!acceptLanguage) {
            return DEFAULT_LANGUAGE;
        }

        // Parse Accept-Language header
        const languages = acceptLanguage
            .split(',')
            .map(lang => {
                const [code, quality = '1'] = lang.trim().split(';q=');
                return {
                    code: code.toLowerCase().split('-')[0], // Get base language code
                    quality: parseFloat(quality)
                };
            })
            .sort((a, b) => b.quality - a.quality);

        // Find first supported language
        for (const lang of languages) {
            if (Object.values(Language).includes(lang.code as Language)) {
                return lang.code;
            }
        }

        return DEFAULT_LANGUAGE;
    }

    /**
     * Get all supported languages
     */
    getSupportedLanguages(): Language[] {
        return Object.values(Language);
    }

    /**
     * Check if language is supported
     */
    isLanguageSupported(lang: string): boolean {
        return Object.values(Language).includes(lang as Language);
    }

    /**
     * Get language name
     */
    getLanguageName(lang: string): string {
        const names = {
            [Language.EN]: 'English',
            [Language.VI]: 'Tiếng Việt',
            [Language.ZH]: '中文'
        };
        return names[lang] || lang;
    }

    /**
     * Format currency amount with localization
     */
    formatCurrency(amount: number, currency: string, lang: string = DEFAULT_LANGUAGE): string {
        const locale = this.getLocaleFromLanguage(lang);
        
        try {
            return new Intl.NumberFormat(locale, {
                style: 'currency',
                currency: currency,
                minimumFractionDigits: currency === 'VND' ? 0 : 2,
                maximumFractionDigits: currency === 'VND' ? 0 : 2
            }).format(amount);
        } catch (error) {
            // Fallback formatting
            const symbol = this.getCurrencySymbol(currency);
            return `${symbol}${amount.toLocaleString(locale)}`;
        }
    }

    /**
     * Format number with localization
     */
    formatNumber(number: number, lang: string = DEFAULT_LANGUAGE): string {
        const locale = this.getLocaleFromLanguage(lang);
        return number.toLocaleString(locale);
    }

    /**
     * Format date with localization
     */
    formatDate(date: Date, lang: string = DEFAULT_LANGUAGE): string {
        const locale = this.getLocaleFromLanguage(lang);
        return date.toLocaleDateString(locale);
    }

    /**
     * Get locale string from language code
     */
    private getLocaleFromLanguage(lang: string): string {
        const locales = {
            [Language.EN]: 'en-US',
            [Language.VI]: 'vi-VN',
            [Language.ZH]: 'zh-CN'
        };
        return locales[lang] || 'en-US';
    }

    /**
     * Get currency symbol
     */
    private getCurrencySymbol(currency: string): string {
        const symbols = {
            'USD': '$',
            'VND': '₫',
            'CNY': '¥'
        };
        return symbols[currency] || currency;
    }
}
