# 🔐 Google OAuth2 Production Implementation Guide

## 📋 Overview

This is a production-ready Google OAuth2 implementation for NestJS with mobile app deep link integration.

## 🏗️ Architecture

```mermaid
sequenceDiagram
    participant Mobile as Mobile App
    participant Browser as Browser
    participant Backend as NestJS Backend
    participant Google as Google OAuth2
    participant DB as <PERSON>

    Mobile->>Browser: Open /auth/google
    Browser->>Backend: GET /auth/google
    Backend->>Google: Redirect to OAuth consent
    Google->>User: Show consent screen
    User->>Google: Grant permission
    Google->>Backend: GET /auth/google/callback?code=...
    Backend->>Google: Exchange code for tokens
    Google->>Backend: Return user profile
    Backend->>DB: Create/Update user
    Backend->>Backend: Generate JWT tokens
    Backend->>Mobile: Redirect to deep link with tokens
    Mobile->>Mobile: Save tokens & navigate to home
```

## 🛠️ Implementation

### 1. **Environment Configuration**

```env
# Google OAuth2 Configuration
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=https://your-domain.com/api/v1/auth/google/callback

# Mobile App Deep Link
MOBILE_APP_SCHEME=myapp
MOBILE_DEEP_LINK=myapp://oauth-callback

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
AC_TOKEN_EXPIRED=15m
RF_TOKEN_EXPIRED=7d

# Security
BCRYPT_ROUNDS=12
```

### 2. **Module Structure**

```
src/modules/auth/
├── controllers/
│   └── auth.controller.ts          # OAuth endpoints
├── services/
│   └── auth.service.ts             # Business logic
├── google/
│   └── google.strategy.ts          # Passport strategy
├── dto/
│   └── google-oauth.dto.ts         # Data validation
├── interfaces/
│   └── google-oauth.interface.ts   # Type definitions
└── auth.module.ts                  # Module configuration
```

### 3. **Key Features**

#### ✅ **Security**
- Input validation and sanitization
- CSRF protection with state parameter
- Secure headers (HSTS, CSP, etc.)
- Error message sanitization
- Rate limiting ready
- Request ID tracking

#### ✅ **Logging & Monitoring**
- Comprehensive request logging
- Performance metrics
- Error tracking with stack traces
- Audit trail for OAuth events
- Request correlation IDs

#### ✅ **Error Handling**
- Graceful error handling
- User-friendly error messages
- Deep link error redirects
- Fallback mechanisms

#### ✅ **Production Ready**
- Environment-based configuration
- Swagger API documentation
- TypeScript strict mode
- Comprehensive validation
- Database transaction safety

## 🚀 API Endpoints

### 1. **Initiate OAuth2**
```http
GET /api/v1/auth/google
```
- **Purpose**: Redirects to Google OAuth2 consent screen
- **Usage**: Mobile app opens this URL in browser
- **Response**: HTTP 302 redirect to Google

### 2. **OAuth2 Callback**
```http
GET /api/v1/auth/google/callback?code=...&state=...
```
- **Purpose**: Handles Google OAuth2 callback
- **Process**:
  1. Validates callback parameters
  2. Exchanges code for tokens
  3. Creates/updates user in database
  4. Generates JWT tokens
  5. Redirects to mobile app via deep link

**Success Response:**
```
myapp://oauth-callback?accessToken=...&refreshToken=...&userEmail=...&userId=...
```

**Error Response:**
```
myapp://oauth-callback?error=auth_failed&message=...&requestId=...
```

## 📱 Mobile App Integration

### Android Setup

#### 1. **AndroidManifest.xml**
```xml
<activity android:name=".MainActivity" android:launchMode="singleTop">
    <!-- Existing intent filters -->
    
    <!-- Deep link intent filter -->
    <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="myapp" />
    </intent-filter>
</activity>
```

#### 2. **React Native Code**
```javascript
import { Linking } from 'react-native';

// Initiate OAuth
const startGoogleLogin = () => {
  const authUrl = 'https://your-domain.com/api/v1/auth/google';
  Linking.openURL(authUrl);
};

// Handle deep link
useEffect(() => {
  const handleUrl = (event) => {
    if (event.url.startsWith('myapp://oauth-callback')) {
      const params = new URLSearchParams(event.url.split('?')[1]);
      
      if (params.get('accessToken')) {
        // Success - save tokens
        const accessToken = params.get('accessToken');
        const refreshToken = params.get('refreshToken');
        
        AsyncStorage.setItem('accessToken', accessToken);
        AsyncStorage.setItem('refreshToken', refreshToken);
        
        navigation.reset({ index: 0, routes: [{ name: 'Home' }] });
      } else if (params.get('error')) {
        // Error - show message
        Alert.alert('Login Failed', params.get('message'));
      }
    }
  };

  const subscription = Linking.addEventListener('url', handleUrl);
  return () => subscription?.remove();
}, []);
```

### iOS Setup

#### 1. **Info.plist**
```xml
<key>CFBundleURLTypes</key>
<array>
    <dict>
        <key>CFBundleURLName</key>
        <string>myapp</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>myapp</string>
        </array>
    </dict>
</array>
```

## 🔧 Google Console Setup

### 1. **Create OAuth2 Credentials**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 Client ID:
   - **Application type**: Web application
   - **Authorized redirect URIs**: 
     - `https://your-domain.com/api/v1/auth/google/callback`

### 2. **Configure Scopes**
Required scopes:
- `email` - User email address
- `profile` - Basic profile information
- `openid` - OpenID Connect

## 🛡️ Security Considerations

### 1. **Environment Variables**
- Never commit secrets to version control
- Use different credentials for dev/staging/prod
- Rotate secrets regularly

### 2. **HTTPS Only**
- Always use HTTPS in production
- Configure HSTS headers
- Use secure cookies

### 3. **Input Validation**
- Validate all OAuth callback parameters
- Sanitize error messages
- Implement rate limiting

### 4. **Token Security**
- Use short-lived access tokens (15 minutes)
- Implement token refresh mechanism
- Store refresh tokens securely

## 📊 Monitoring & Logging

### 1. **Key Metrics**
- OAuth success/failure rates
- Response times
- Error types and frequencies
- User conversion rates

### 2. **Log Events**
- OAuth initiation
- Callback received
- User creation/login
- Token generation
- Errors and exceptions

### 3. **Alerts**
- High error rates
- Slow response times
- Failed authentications
- Security incidents

## 🧪 Testing

### 1. **Unit Tests**
```javascript
describe('GoogleStrategy', () => {
  it('should validate Google profile', async () => {
    // Test profile validation
  });
  
  it('should create new user', async () => {
    // Test user creation
  });
});
```

### 2. **Integration Tests**
```javascript
describe('OAuth Flow', () => {
  it('should complete full OAuth flow', async () => {
    // Test complete flow
  });
});
```

### 3. **Manual Testing**
1. Test OAuth initiation
2. Test successful callback
3. Test error scenarios
4. Test deep link handling
5. Test token validation

## 🚨 Troubleshooting

### Common Issues

1. **"redirect_uri_mismatch"**
   - Check callback URL in Google Console
   - Ensure exact match with environment variable

2. **"invalid_client"**
   - Verify client ID and secret
   - Check environment configuration

3. **Deep link not working**
   - Verify mobile app configuration
   - Test deep link manually
   - Check URL scheme registration

4. **Token validation fails**
   - Check JWT secret configuration
   - Verify token expiration settings
   - Test with fresh tokens

## 📈 Performance Optimization

1. **Database Queries**
   - Use indexes on email and googleId
   - Implement connection pooling
   - Cache user data when appropriate

2. **Response Times**
   - Minimize external API calls
   - Implement request timeouts
   - Use async/await properly

3. **Memory Usage**
   - Clean up unused objects
   - Implement proper error handling
   - Monitor memory leaks

## 🔄 Deployment

### 1. **Environment Setup**
```bash
# Production environment
NODE_ENV=production
GOOGLE_CLIENT_ID=prod-client-id
GOOGLE_CLIENT_SECRET=prod-secret
GOOGLE_CALLBACK_URL=https://api.yourdomain.com/api/v1/auth/google/callback
```

### 2. **Health Checks**
```javascript
// Add health check endpoint
@Get('health')
async healthCheck() {
  return {
    status: 'OK',
    oauth: {
      google: config.GOOGLE_CLIENT_ID ? 'configured' : 'missing'
    }
  };
}
```

This implementation provides a robust, secure, and production-ready Google OAuth2 solution for mobile applications.
