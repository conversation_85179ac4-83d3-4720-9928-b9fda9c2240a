import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ExchangeRate } from '../entity/exchange-rate.entity';
import { Currency, BASE_CURRENCY } from '../enums/currency.enum';
import { Cron, CronExpression } from '@nestjs/schedule';
import axios from 'axios';

@Injectable()
export class CurrencyService implements OnModuleInit {
    private readonly logger = new Logger(CurrencyService.name);
    private readonly EXCHANGE_API_URL = 'https://api.exchangerate-api.com/v4/latest';
    
    constructor(
        @InjectRepository(ExchangeRate)
        private readonly exchangeRateRepo: Repository<ExchangeRate>
    ) {}

    async onModuleInit() {
        // Initialize exchange rates on startup
        await this.updateExchangeRates();
    }

    /**
     * Get exchange rate between two currencies
     */
    async getExchangeRate(fromCurrency: string, toCurrency: string): Promise<number> {
        if (fromCurrency === toCurrency) {
            return 1.0;
        }

        // Try to get from database first
        const exchangeRate = await this.exchangeRateRepo.findOne({
            where: {
                from_currency: fromCurrency,
                to_currency: toCurrency,
                is_active: true
            },
            order: { effective_date: 'DESC' }
        });

        if (exchangeRate && this.isRateValid(exchangeRate.effective_date)) {
            return exchangeRate.rate;
        }

        // If not found or expired, fetch from API
        return await this.fetchAndSaveExchangeRate(fromCurrency, toCurrency);
    }

    /**
     * Convert amount from one currency to another
     */
    async convertAmount(amount: number, fromCurrency: string, toCurrency: string): Promise<number> {
        if (fromCurrency === toCurrency) {
            return amount;
        }

        const rate = await this.getExchangeRate(fromCurrency, toCurrency);
        return Math.round(amount * rate * 100) / 100; // Round to 2 decimal places
    }

    /**
     * Convert amount to base currency (USD)
     */
    async convertToBaseCurrency(amount: number, fromCurrency: string): Promise<number> {
        return await this.convertAmount(amount, fromCurrency, BASE_CURRENCY);
    }

    /**
     * Convert amount from base currency to target currency
     */
    async convertFromBaseCurrency(amount: number, toCurrency: string): Promise<number> {
        return await this.convertAmount(amount, BASE_CURRENCY, toCurrency);
    }

    /**
     * Update exchange rates from external API (runs daily at 6 AM)
     */
    @Cron(CronExpression.EVERY_DAY_AT_6AM)
    async updateExchangeRates(): Promise<void> {
        this.logger.log('Starting exchange rates update...');
        
        try {
            const currencies = Object.values(Currency);
            
            for (const baseCurrency of currencies) {
                const response = await axios.get(`${this.EXCHANGE_API_URL}/${baseCurrency}`);
                const rates = response.data.rates;
                
                for (const targetCurrency of currencies) {
                    if (baseCurrency !== targetCurrency && rates[targetCurrency]) {
                        await this.saveExchangeRate(
                            baseCurrency,
                            targetCurrency,
                            rates[targetCurrency]
                        );
                    }
                }
            }
            
            this.logger.log('Exchange rates updated successfully');
        } catch (error) {
            this.logger.error('Failed to update exchange rates:', error.message);
        }
    }

    /**
     * Save exchange rate to database
     */
    private async saveExchangeRate(fromCurrency: string, toCurrency: string, rate: number): Promise<void> {
        const exchangeRate = this.exchangeRateRepo.create({
            from_currency: fromCurrency,
            to_currency: toCurrency,
            rate: rate,
            effective_date: new Date(),
            is_active: true
        });

        await this.exchangeRateRepo.save(exchangeRate);
    }

    /**
     * Fetch exchange rate from API and save to database
     */
    private async fetchAndSaveExchangeRate(fromCurrency: string, toCurrency: string): Promise<number> {
        try {
            const response = await axios.get(`${this.EXCHANGE_API_URL}/${fromCurrency}`);
            const rate = response.data.rates[toCurrency];
            
            if (rate) {
                await this.saveExchangeRate(fromCurrency, toCurrency, rate);
                return rate;
            }
            
            throw new Error(`Exchange rate not found for ${fromCurrency} to ${toCurrency}`);
        } catch (error) {
            this.logger.error(`Failed to fetch exchange rate: ${error.message}`);
            // Return 1.0 as fallback
            return 1.0;
        }
    }

    /**
     * Check if exchange rate is still valid (within 24 hours)
     */
    private isRateValid(effectiveDate: Date): boolean {
        const now = new Date();
        const diffInHours = (now.getTime() - effectiveDate.getTime()) / (1000 * 60 * 60);
        return diffInHours < 24;
    }

    /**
     * Get all supported currencies
     */
    getSupportedCurrencies(): Currency[] {
        return Object.values(Currency);
    }

    /**
     * Get currency symbol
     */
    getCurrencySymbol(currency: string): string {
        const symbols = {
            [Currency.USD]: '$',
            [Currency.VND]: '₫',
            [Currency.CNY]: '¥'
        };
        return symbols[currency] || currency;
    }
}
