import { IsEnum, IsOptional, IsDateString, <PERSON>Int, <PERSON><PERSON><PERSON><PERSON>, IsString, Min, Max } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';

// Query parameters cho biểu đồ thu chi
export class IncomeExpenseChartQueryDto {
    @ApiProperty({
        description: 'Time period for chart data',
        enum: ['week', 'month', 'year'],
        example: 'month'
    })
    @IsEnum(['week', 'month', 'year'], { message: 'Period must be one of: week, month, year' })
    period: 'week' | 'month' | 'year';

    @ApiPropertyOptional({
        description: 'Start date for custom range (ISO string)',
        example: '2024-01-01T00:00:00.000Z'
    })
    @IsOptional()
    @IsDateString({}, { message: 'Start date must be a valid ISO date string' })
    startDate?: string;

    @ApiPropertyOptional({
        description: 'End date for custom range (ISO string)',
        example: '2024-12-31T23:59:59.999Z'
    })
    @IsOptional()
    @IsDateString({}, { message: 'End date must be a valid ISO date string' })
    endDate?: string;
}

// Query parameters cho phân tích danh mục
export class CategoryAnalysisQueryDto {
    @ApiProperty({
        description: 'Time period for analysis',
        enum: ['month', 'year'],
        example: 'month'
    })
    @IsEnum(['month', 'year'], { message: 'Period must be one of: month, year' })
    period: 'month' | 'year';

    @ApiPropertyOptional({
        description: 'Transaction type filter',
        enum: ['income', 'expense'],
        example: 'expense'
    })
    @IsOptional()
    @IsEnum(['income', 'expense'], { message: 'Type must be one of: income, expense' })
    type?: 'income' | 'expense';

    @ApiPropertyOptional({
        description: 'Month (1-12) for monthly analysis',
        example: 12,
        minimum: 1,
        maximum: 12
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt({ message: 'Month must be an integer' })
    @Min(1, { message: 'Month must be between 1 and 12' })
    @Max(12, { message: 'Month must be between 1 and 12' })
    month?: number;

    @ApiPropertyOptional({
        description: 'Year for analysis',
        example: 2024,
        minimum: 2020,
        maximum: 2030
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt({ message: 'Year must be an integer' })
    @Min(2020, { message: 'Year must be between 2020 and 2030' })
    @Max(2030, { message: 'Year must be between 2020 and 2030' })
    year?: number;
}

// Query parameters cho xu hướng tài chính
export class FinancialTrendsQueryDto {
    @ApiProperty({
        description: 'Time period for trend analysis',
        enum: ['month', 'year'],
        example: 'month'
    })
    @IsEnum(['month', 'year'], { message: 'Period must be one of: month, year' })
    period: 'month' | 'year';

    @ApiPropertyOptional({
        description: 'Number of periods to analyze (default: 12 for month, 5 for year)',
        example: 12,
        minimum: 3,
        maximum: 24
    })
    @IsOptional()
    @Type(() => Number)
    @IsInt({ message: 'Limit must be an integer' })
    @Min(3, { message: 'Limit must be at least 3' })
    @Max(24, { message: 'Limit must be at most 24' })
    limit?: number;
}

// Query parameters cho so sánh
export class ComparisonQueryDto {
    @ApiProperty({
        description: 'Array of periods to compare (format: YYYY-MM for month, YYYY for year)',
        example: ['2024-01', '2024-02', '2024-03'],
        type: [String]
    })
    @IsArray({ message: 'Periods must be an array' })
    @IsString({ each: true, message: 'Each period must be a string' })
    @Transform(({ value }) => Array.isArray(value) ? value : [value])
    periods: string[];

    @ApiProperty({
        description: 'Type of period comparison',
        enum: ['month', 'year'],
        example: 'month'
    })
    @IsEnum(['month', 'year'], { message: 'Period type must be one of: month, year' })
    periodType: 'month' | 'year';
}

// Response DTOs
export class IncomeExpenseChartDto {
    @ApiProperty({
        description: 'Chart labels',
        example: ['Week 1', 'Week 2', 'Week 3', 'Week 4']
    })
    labels: string[];

    @ApiProperty({
        description: 'Income data points',
        example: [1000000, 1500000, 1200000, 1800000]
    })
    incomeData: number[];

    @ApiProperty({
        description: 'Expense data points',
        example: [800000, 1200000, 900000, 1400000]
    })
    expenseData: number[];

    @ApiProperty({
        description: 'Total income for the period',
        example: 5500000
    })
    totalIncome: number;

    @ApiProperty({
        description: 'Total expense for the period',
        example: 4300000
    })
    totalExpense: number;

    @ApiProperty({
        description: 'Net amount (income - expense)',
        example: 1200000
    })
    netAmount: number;
}

export class CategoryAnalysisDto {
    @ApiProperty({
        description: 'Category breakdown data',
        type: 'array',
        items: {
            type: 'object',
            properties: {
                categoryId: { type: 'string', example: 'uuid-string' },
                categoryName: { type: 'string', example: 'Food & Dining' },
                amount: { type: 'number', example: 500000 },
                percentage: { type: 'number', example: 25.5 },
                color: { type: 'string', example: '#FF6384' }
            }
        }
    })
    categories: {
        categoryId: string;
        categoryName: string;
        amount: number;
        percentage: number;
        color?: string;
    }[];

    @ApiProperty({
        description: 'Total amount for all categories',
        example: 2000000
    })
    totalAmount: number;

    @ApiProperty({
        description: 'Analysis period',
        example: '2024-12'
    })
    period: string;
}

export class FinancialTrendsDto {
    @ApiProperty({
        description: 'Trend data points',
        type: 'array',
        items: {
            type: 'object',
            properties: {
                period: { type: 'string', example: '2024-01' },
                income: { type: 'number', example: 3000000 },
                expense: { type: 'number', example: 2500000 },
                balance: { type: 'number', example: 500000 },
                growth: { type: 'number', example: 5.2 },
                movingAverage: { type: 'number', example: 480000 }
            }
        }
    })
    trends: {
        period: string;
        income: number;
        expense: number;
        balance: number;
        growth: number;
        movingAverage?: number;
    }[];

    @ApiProperty({
        description: 'Overall growth rate percentage',
        example: 12.5
    })
    overallGrowth: number;

    @ApiProperty({
        description: 'Average monthly/yearly balance',
        example: 520000
    })
    averageBalance: number;
}

export class ComparisonDto {
    @ApiProperty({
        description: 'Comparison data for each period',
        type: 'array',
        items: {
            type: 'object',
            properties: {
                period: { type: 'string', example: '2024-01' },
                income: { type: 'number', example: 3000000 },
                expense: { type: 'number', example: 2500000 },
                balance: { type: 'number', example: 500000 },
                transactionCount: { type: 'number', example: 45 }
            }
        }
    })
    periods: {
        period: string;
        income: number;
        expense: number;
        balance: number;
        transactionCount: number;
    }[];

    @ApiProperty({
        description: 'Best performing period',
        example: '2024-03'
    })
    bestPeriod: string;

    @ApiProperty({
        description: 'Worst performing period',
        example: '2024-01'
    })
    worstPeriod: string;
}
