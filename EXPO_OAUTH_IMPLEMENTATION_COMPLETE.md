# ✅ Expo OAuth Implementation Complete

## 🎯 **Problem Solved**

**Issue:** Google OAuth flow bị treo vì backend redirect về custom scheme `financeapp://` nhưng Expo Go không support custom scheme, chỉ support `exp://` scheme.

**Solution:** Implement environment-specific redirect logic:
- **Development**: Redirect về Expo scheme `exp://*************:3001/--/auth/callback`
- **Production**: Redirect về custom scheme `financeapp://auth/callback`

## ✅ **Implementation Details**

### **1. Updated Configuration**

**File: `src/config/config.service.ts`**
```typescript
// Expo Development Configuration
EXPO_HOST = env.EXPO_HOST || '*************:3001'
EXPO_SCHEME = env.EXPO_SCHEME || 'exp'
```

**File: `.env`**
```bash
# Expo Development Configuration
EXPO_HOST=*************:3001
EXPO_SCHEME=exp
```

### **2. Smart Redirect Logic**

**File: `src/modules/auth/controllers/auth.controller.ts`**

**Success Redirect:**
```typescript
private getRedirectUrl(accessToken: string, refreshToken: string, userEmail?: string): string {
    const isDevelopment = config.NODE_ENV === 'development';
    
    const params = new URLSearchParams({
        accessToken,
        refreshToken,
        status: 'success'
    });

    if (userEmail) {
        params.set('user_email', userEmail);
    }

    if (isDevelopment) {
        // Expo Go scheme for development
        return `${config.EXPO_SCHEME}://${config.EXPO_HOST}/--/auth/callback?${params.toString()}`;
    } else {
        // Custom scheme for production
        return `${config.MOBILE_DEEP_LINK}?${params.toString()}`;
    }
}
```

**Error Redirect:**
```typescript
private getErrorRedirectUrl(errorMessage: string): string {
    const isDevelopment = config.NODE_ENV === 'development';
    
    const params = new URLSearchParams({
        status: 'error',
        message: errorMessage
    });

    if (isDevelopment) {
        // Expo Go scheme for development
        return `${config.EXPO_SCHEME}://${config.EXPO_HOST}/--/auth/callback?${params.toString()}`;
    } else {
        // Custom scheme for production
        return `${config.MOBILE_DEEP_LINK}?${params.toString()}`;
    }
}
```

## 🔧 **Current Configuration**

### **Server Configuration**
- **Server URL**: `http://*************:3001`
- **Google OAuth Callback**: `https://f888-27-72-63-114.ngrok-free.app/api/v1/auth/google/callback`
- **Environment**: `development`

### **Redirect URLs**

#### **Development (Expo Go)**
- **Success**: `exp://*************:3001/--/auth/callback?accessToken=xxx&refreshToken=yyy&status=success&user_email=xxx`
- **Error**: `exp://*************:3001/--/auth/callback?status=error&message=xxx`

#### **Production (Standalone App)**
- **Success**: `financeapp://auth/callback?accessToken=xxx&refreshToken=yyy&status=success&user_email=xxx`
- **Error**: `financeapp://auth/callback?status=error&message=xxx`

## 📱 **Mobile App Integration**

### **React Native/Expo App Setup**

**1. Install Dependencies:**
```bash
expo install expo-linking
```

**2. Configure Deep Links in app.json:**
```json
{
  "expo": {
    "scheme": "financeapp",
    "web": {
      "bundler": "metro"
    }
  }
}
```

**3. Handle Deep Links:**
```javascript
import * as Linking from 'expo-linking';
import { useEffect } from 'react';

export default function App() {
  useEffect(() => {
    const handleDeepLink = (event) => {
      const url = event.url;
      console.log('Deep link received:', url);
      
      // Handle both Expo and custom schemes
      if (url.includes('/--/auth/callback') || url.includes('financeapp://auth/callback')) {
        const params = new URLSearchParams(url.split('?')[1]);
        
        if (params.get('status') === 'success') {
          const accessToken = params.get('accessToken');
          const refreshToken = params.get('refreshToken');
          const userEmail = params.get('user_email');
          
          console.log('OAuth Success:', { accessToken, refreshToken, userEmail });
          // Store tokens and navigate to main app
          handleOAuthSuccess(accessToken, refreshToken, userEmail);
        } else {
          const errorMessage = params.get('message');
          console.log('OAuth Error:', errorMessage);
          handleOAuthError(errorMessage);
        }
      }
    };

    // Listen for deep links
    const subscription = Linking.addEventListener('url', handleDeepLink);
    
    // Check if app was opened via deep link
    Linking.getInitialURL().then((url) => {
      if (url) {
        handleDeepLink({ url });
      }
    });

    return () => subscription?.remove();
  }, []);

  const initiateGoogleOAuth = () => {
    const oauthUrl = 'http://*************:3001/api/v1/auth/google';
    Linking.openURL(oauthUrl);
  };

  // Rest of your app...
}
```

## 🚀 **Testing Flow**

### **1. Development Testing (Expo Go)**
1. Start backend: `npm run dev` (runs on port 3001)
2. Start Expo app: `expo start`
3. In Expo app, call: `Linking.openURL('http://*************:3001/api/v1/auth/google')`
4. Complete Google OAuth in browser
5. Should redirect to: `exp://*************:3001/--/auth/callback?accessToken=...&status=success`
6. Expo Go will open your app with the tokens

### **2. Production Testing (Standalone App)**
1. Build standalone app with custom scheme `financeapp://`
2. OAuth will redirect to: `financeapp://auth/callback?accessToken=...&status=success`
3. Your app will handle the custom scheme

## 🔍 **Debug & Monitoring**

### **Backend Logs to Watch**
```
🔗 [oauth_xxx] Redirecting to: exp://*************:3001/--/auth/callback?accessToken=***&refreshToken=***&status=success
```

### **Mobile App Logs**
```javascript
console.log('Deep link received:', url);
console.log('OAuth Success:', { accessToken, refreshToken, userEmail });
```

## ✨ **Key Benefits**

1. **Environment Aware**: Automatically detects development vs production
2. **Expo Go Compatible**: Works seamlessly with Expo Go during development
3. **Production Ready**: Supports custom schemes for standalone apps
4. **Error Handling**: Comprehensive error redirect logic
5. **Security**: Tokens are passed securely via deep links
6. **User Experience**: Smooth OAuth flow without getting stuck in browser

## 🎉 **Ready to Test**

The implementation is now complete and ready for testing! The OAuth flow will:
- Work in Expo Go during development
- Work in standalone apps in production
- Handle both success and error cases
- Provide comprehensive logging for debugging

**Next Steps:**
1. Test OAuth flow in Expo Go
2. Verify token handling in mobile app
3. Test error scenarios
4. Build standalone app for production testing
