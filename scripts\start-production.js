#!/usr/bin/env node

/**
 * Production startup script
 * Sets NODE_ENV=production and starts the application
 */

const { spawn } = require('child_process');
const path = require('path');

// Set environment to production
process.env.NODE_ENV = 'production';

console.log('🚀 Starting Finance App in PRODUCTION mode...');
console.log('📄 Loading .env.production file...');

// Start the application
const child = spawn('node', ['dist/main.js'], {
    stdio: 'inherit',
    env: {
        ...process.env,
        NODE_ENV: 'production'
    }
});

child.on('error', (error) => {
    console.error('❌ Failed to start application:', error);
    process.exit(1);
});

child.on('exit', (code) => {
    console.log(`🏁 Application exited with code ${code}`);
    process.exit(code);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT, shutting down gracefully...');
    child.kill('SIGINT');
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
    child.kill('SIGTERM');
});
