import { Injectable, BadRequestException, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, DataSource, QueryRunner } from 'typeorm';
import { Transaction } from '../entity/transaction.entity';
import { Category } from '../entity/category.entity';
import { Budget } from '../entity/budget.entity';
import { User } from '@modules/users/entity';
import {
    CreateTransactionDto,
    UpdateTransactionDto,
    TransactionQueryDto,
    TransactionStatisticsDto,
    CategoryStatisticsDto
} from '../dto/transaction.dto';
import { CategoryType, TransactionType, BASE_CURRENCY } from '../enums';
import { createPaginationMeta } from '@base/api/utils/pagination.util';
import { CurrencyService } from './currency.service';

@Injectable()
export class TransactionService {
    private readonly logger = new Logger(TransactionService.name);

    constructor(
        @InjectRepository(Transaction)
        private readonly transactionRepo: Repository<Transaction>,
        @InjectRepository(Category)
        private readonly categoryRepo: Repository<Category>,
        @InjectRepository(Budget)
        private readonly budgetRepo: Repository<Budget>,
        @InjectRepository(User)
        private readonly userRepo: Repository<User>,
        private dataSource: DataSource,
        private readonly currencyService: CurrencyService
    ) { }

    async create(dto: CreateTransactionDto, userId: string): Promise<Transaction> {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            // 1. Validate and get category if provided
            const category = await this.validateAndGetCategory(dto.category_id, userId, dto.type);

            // 2. Handle currency conversion
            const originalCurrency = dto.original_currency || BASE_CURRENCY;
            let convertedAmount = dto.amount;
            let exchangeRate = 1.0;
            let originalAmount = dto.amount;

            if (originalCurrency !== BASE_CURRENCY) {
                // Convert to base currency (USD)
                convertedAmount = await this.currencyService.convertToBaseCurrency(dto.amount, originalCurrency);
                exchangeRate = await this.currencyService.getExchangeRate(originalCurrency, BASE_CURRENCY);
                originalAmount = dto.amount;
            }

            // 3. Create transaction
            const transaction = this.transactionRepo.create({
                ...dto,
                user_id: userId,
                amount: convertedAmount, // Store in base currency
                original_currency: originalCurrency,
                original_amount: originalAmount,
                exchange_rate: exchangeRate
            });

            await queryRunner.manager.save(transaction);

            // 4. Update user balance (use converted amount)
            await this.updateUserBalance(queryRunner, userId, convertedAmount, dto.type, true);

            // 5. Update budget if applicable (use converted amount)
            if (category && dto.type === TransactionType.EXPENSE) {
                await this.updateBudgetSpent(
                    queryRunner,
                    dto.category_id!,
                    userId,
                    dto.transaction_date,
                    convertedAmount,
                    true
                );
            }

            await queryRunner.commitTransaction();
            this.logger.log(`Transaction created successfully: ${transaction.id}`);

            return await this.findOne(transaction.id, userId);

        } catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`Failed to create transaction: ${error.message}`, error.stack);
            throw error;
        } finally {
            await queryRunner.release();
        }
    }

    async findAll(query: TransactionQueryDto, userId: string) {
        try {
            const qb = this.transactionRepo.createQueryBuilder('transaction')
                .where('transaction.user_id = :userId', { userId })
                .leftJoinAndSelect('transaction.category', 'category');

            // Apply filters
            this.applyDateFilters(qb, query);
            this.applyTypeFilter(qb, query);
            this.applyCategoryFilter(qb, query);
            this.applySearchFilter(qb, query);

            // Apply sorting
            const sortColumn = query.sort_by === 'created_at' ? 'transaction.created_at' : `transaction.${query.sort_by}`;
            qb.orderBy(sortColumn, query.sort_order);

            // Apply pagination
            const [items, total] = await qb
                .skip((query.page - 1) * query.limit)
                .take(query.limit)
                .getManyAndCount();

            return {
                data: items,
                meta: createPaginationMeta({
                    totalItems: total,
                    query: { page: query.page, limit: query.limit },
                    itemCount: items.length
                })
            };

        } catch (error) {
            this.logger.error(`Failed to fetch transactions: ${error.message}`, error.stack);
            throw error;
        }
    }

    async findOne(id: string, userId: string): Promise<Transaction> {
        const transaction = await this.transactionRepo.findOne({
            where: { id, user_id: userId },
            relations: ['category']
        });

        if (!transaction) {
            throw new NotFoundException(`Transaction with ID ${id} not found`);
        }

        return transaction;
    }

    async update(id: string, dto: UpdateTransactionDto, userId: string): Promise<Transaction> {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            // Get original transaction
            const originalTransaction = await this.findOne(id, userId);

            // Store original values
            const originalAmount = originalTransaction.amount;
            const originalType = originalTransaction.type;
            const originalCategoryId = originalTransaction.category_id;
            const originalDate = originalTransaction.transaction_date;

            // Prepare new values (use original if not provided in dto)
            const newAmount = dto.amount !== undefined ? dto.amount : originalAmount;
            const newType = dto.type !== undefined ? dto.type : originalType;
            const newCategoryId = dto.category_id !== undefined ? dto.category_id : originalCategoryId;
            const newDate = dto.transaction_date !== undefined ? dto.transaction_date : originalDate;

            // Validate new category if provided
            if (dto.category_id !== undefined) {
                await this.validateAndGetCategory(dto.category_id, userId, newType);
            }

            // Calculate differences for smart updates
            const amountDiff = newAmount - originalAmount;
            const typeChanged = newType !== originalType;
            const categoryChanged = newCategoryId !== originalCategoryId;
            const dateChanged = newDate.getTime() !== originalDate.getTime();

            // Update user balance efficiently
            if (amountDiff !== 0 || typeChanged) {
                await this.updateUserBalanceSmartly(
                    queryRunner,
                    userId,
                    originalAmount,
                    originalType,
                    newAmount,
                    newType
                );
            }

            // Update budget efficiently
            if (originalType === TransactionType.EXPENSE || newType === TransactionType.EXPENSE) {
                await this.updateBudgetSmartly(
                    queryRunner,
                    userId,
                    originalAmount,
                    originalType,
                    originalCategoryId,
                    originalDate,
                    newAmount,
                    newType,
                    newCategoryId,
                    newDate
                );
            }

            // Update transaction
            Object.assign(originalTransaction, dto);
            await queryRunner.manager.save(originalTransaction);

            await queryRunner.commitTransaction();
            this.logger.log(`Transaction updated successfully: ${id}`);

            return await this.findOne(id, userId);

        } catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`Failed to update transaction: ${error.message}`, error.stack);
            throw error;
        } finally {
            await queryRunner.release();
        }
    }

    async remove(id: string, userId: string): Promise<boolean> {
        const queryRunner = this.dataSource.createQueryRunner();
        await queryRunner.connect();
        await queryRunner.startTransaction();

        try {
            const transaction = await this.findOne(id, userId);

            // Revert transaction effects
            await this.revertTransactionEffects(
                queryRunner,
                userId,
                transaction.amount,
                transaction.type,
                transaction.category_id,
                transaction.transaction_date
            );

            await queryRunner.manager.remove(transaction);
            await queryRunner.commitTransaction();

            this.logger.log(`Transaction deleted successfully: ${id}`);
            return true;

        } catch (error) {
            await queryRunner.rollbackTransaction();
            this.logger.error(`Failed to delete transaction: ${error.message}`, error.stack);
            throw error;
        } finally {
            await queryRunner.release();
        }
    }

    async getStatistics(userId: string, startDate: Date, endDate: Date): Promise<TransactionStatisticsDto> {
        try {
            // Validate date range
            if (startDate > endDate) {
                throw new BadRequestException('Start date cannot be after end date');
            }

            // Get all transactions in date range
            const transactions = await this.transactionRepo.find({
                where: {
                    user_id: userId,
                    transaction_date: Between(startDate, endDate)
                },
                relations: ['category']
            });

            if (transactions.length === 0) {
                return this.getEmptyStatistics();
            }

            // Calculate basic statistics
            const { total_income, total_expense } = this.calculateBasicStats(transactions);

            // Calculate category statistics
            const categories = this.calculateCategoryStats(transactions, total_income + total_expense);

            const totalAmount = total_income + total_expense;
            const averageAmount = totalAmount > 0 ? totalAmount / transactions.length : 0;

            return {
                total_income,
                total_expense,
                net_amount: total_income - total_expense,
                transaction_count: transactions.length,
                average_amount: Math.round(averageAmount),
                categories
            };

        } catch (error) {
            this.logger.error(`Failed to get statistics: ${error.message}`, error.stack);
            throw error;
        }
    }

    async getBudgetStatistics(userId: string, month: number, year: number) {
        try {
            // Validate month and year
            if (month < 1 || month > 12) {
                throw new BadRequestException('Month must be between 1 and 12');
            }

            if (year < 2000 || year > 2100) {
                throw new BadRequestException('Year must be between 2000 and 2100');
            }

            // Get active budgets for the month/year
            const budgets = await this.budgetRepo.find({
                where: {
                    user_id: userId,
                    month,
                    year,
                    is_active: true
                },
                relations: ['category']
            });

            if (budgets.length === 0) {
                return this.getEmptyBudgetStatistics(month, year);
            }

            // Get transactions for the month
            const { startDate, endDate } = this.getMonthDateRange(month, year);
            const transactions = await this.transactionRepo.find({
                where: {
                    user_id: userId,
                    type: TransactionType.EXPENSE,
                    transaction_date: Between(startDate, endDate)
                }
            });

            // Calculate statistics for each budget
            const budgetStats = this.calculateBudgetStats(budgets, transactions);

            // Calculate overall statistics
            const totalBudget = budgetStats.reduce((sum, stat) => sum + stat.budget_amount, 0);
            const totalSpent = budgetStats.reduce((sum, stat) => sum + stat.spent_amount, 0);
            const overallPercentage = totalBudget > 0 ? (totalSpent / totalBudget) * 100 : 0;

            return {
                month,
                year,
                total_budget: totalBudget,
                total_spent: totalSpent,
                remaining_budget: totalBudget - totalSpent,
                overall_percentage: Math.round(overallPercentage * 100) / 100,
                categories: budgetStats.sort((a, b) => b.percentage_used - a.percentage_used)
            };

        } catch (error) {
            this.logger.error(`Failed to get budget statistics: ${error.message}`, error.stack);
            throw error;
        }
    }

    // Private helper methods

    private async validateAndGetCategory(categoryId: string | undefined, userId: string, type: TransactionType): Promise<Category | null> {
        if (!categoryId) {
            return null;
        }

        const category = await this.categoryRepo.findOne({
            where: { id: categoryId, user_id: userId }
        });

        if (!category) {
            throw new NotFoundException(`Category with ID ${categoryId} not found`);
        }

        // Validate transaction type matches category type
        if (category.type === CategoryType.INCOME && type !== TransactionType.INCOME) {
            throw new BadRequestException('Transaction type must be INCOME for income category');
        }
        if (category.type === CategoryType.EXPENSE && type !== TransactionType.EXPENSE) {
            throw new BadRequestException('Transaction type must be EXPENSE for expense category');
        }

        return category;
    }

    private async updateUserBalance(queryRunner: QueryRunner, userId: string, amount: number, type: TransactionType, isAdd: boolean): Promise<void> {
        const user = await queryRunner.manager.findOne(User, { where: { id: userId } });
        if (!user) {
            throw new NotFoundException(`User with ID ${userId} not found`);
        }

        const adjustment = isAdd ? amount : -amount;

        if (type === TransactionType.INCOME) {
            user.initial_balance += adjustment;
        } else {
            user.initial_balance -= adjustment;
        }

        await queryRunner.manager.save(user);
    }

    private async updateUserBalanceSmartly(
        queryRunner: QueryRunner,
        userId: string,
        originalAmount: number,
        originalType: TransactionType,
        newAmount: number,
        newType: TransactionType
    ): Promise<void> {
        const user = await queryRunner.manager.findOne(User, { where: { id: userId } });
        if (!user) {
            throw new NotFoundException(`User with ID ${userId} not found`);
        }

        // Calculate net effect on balance
        let balanceChange = 0;

        // Remove effect of original transaction
        if (originalType === TransactionType.INCOME) {
            balanceChange -= originalAmount; // Remove income
        } else {
            balanceChange += originalAmount; // Remove expense (add back)
        }

        // Add effect of new transaction
        if (newType === TransactionType.INCOME) {
            balanceChange += newAmount; // Add income
        } else {
            balanceChange -= newAmount; // Add expense (subtract)
        }

        user.initial_balance += balanceChange;
        await queryRunner.manager.save(user);
    }

    private async updateBudgetSpent(
        queryRunner: QueryRunner,
        categoryId: string,
        userId: string,
        transactionDate: Date,
        amount: number,
        isAdd: boolean
    ): Promise<void> {
        const month = transactionDate.getMonth() + 1;
        const year = transactionDate.getFullYear();

        const budget = await queryRunner.manager.findOne(Budget, {
            where: {
                category_id: categoryId,
                user_id: userId,
                month,
                year,
                is_active: true
            }
        });

        if (budget) {
            const oldAmount = budget.amount_spent;
            const adjustment = isAdd ? amount : -amount;
            budget.amount_spent = Math.max(0, budget.amount_spent + adjustment);

            this.logger.log(`💰 Budget ${budget.id}: ${oldAmount} ${isAdd ? '+' : '-'} ${amount} = ${budget.amount_spent}`);

            await queryRunner.manager.save(budget);
        } else {
            this.logger.warn(`⚠️ No budget found for category ${categoryId}, month ${month}/${year}`);
        }
    }

    private async updateBudgetSmartly(
        queryRunner: QueryRunner,
        userId: string,
        originalAmount: number,
        originalType: TransactionType,
        originalCategoryId: string | null,
        originalDate: Date,
        newAmount: number,
        newType: TransactionType,
        newCategoryId: string | null,
        newDate: Date
    ): Promise<void> {
        this.logger.log(`🔄 updateBudgetSmartly: ${originalAmount} → ${newAmount}, category: ${originalCategoryId} → ${newCategoryId}`);

        // Handle original budget (remove old expense)
        if (originalType === TransactionType.EXPENSE && originalCategoryId) {
            this.logger.log(`➖ Removing ${originalAmount} from budget ${originalCategoryId}`);
            await this.updateBudgetSpent(
                queryRunner,
                originalCategoryId,
                userId,
                originalDate,
                originalAmount,
                false // Remove
            );
        }

        // Handle new budget (add new expense)
        if (newType === TransactionType.EXPENSE && newCategoryId) {
            this.logger.log(`➕ Adding ${newAmount} to budget ${newCategoryId}`);
            await this.updateBudgetSpent(
                queryRunner,
                newCategoryId,
                userId,
                newDate,
                newAmount,
                true // Add
            );
        }
    }

    private async revertTransactionEffects(
        queryRunner: QueryRunner,
        userId: string,
        amount: number,
        type: TransactionType,
        categoryId: string | null,
        transactionDate: Date
    ): Promise<void> {
        // Revert user balance
        await this.updateUserBalance(queryRunner, userId, amount, type, false);

        // Revert budget if applicable
        if (type === TransactionType.EXPENSE && categoryId) {
            await this.updateBudgetSpent(queryRunner, categoryId, userId, transactionDate, amount, false);
        }
    }

    private async applyTransactionEffects(
        queryRunner: QueryRunner,
        userId: string,
        amount: number,
        type: TransactionType,
        categoryId: string | null,
        transactionDate: Date
    ): Promise<void> {
        // Apply user balance
        await this.updateUserBalance(queryRunner, userId, amount, type, true);

        // Apply budget if applicable
        if (type === TransactionType.EXPENSE && categoryId) {
            await this.updateBudgetSpent(queryRunner, categoryId, userId, transactionDate, amount, true);
        }
    }

    private applyDateFilters(qb: any, query: TransactionQueryDto): void {
        if (query.start_date) {
            qb.andWhere('transaction.transaction_date >= :startDate', {
                startDate: query.start_date
            });
        }

        if (query.end_date) {
            qb.andWhere('transaction.transaction_date <= :endDate', {
                endDate: query.end_date
            });
        }
    }

    private applyTypeFilter(qb: any, query: TransactionQueryDto): void {
        if (query.type) {
            qb.andWhere('transaction.type = :type', { type: query.type });
        }
    }

    private applyCategoryFilter(qb: any, query: TransactionQueryDto): void {
        if (query.category_id) {
            qb.andWhere('transaction.category_id = :categoryId', {
                categoryId: query.category_id
            });
        }
    }

    private applySearchFilter(qb: any, query: TransactionQueryDto): void {
        if (query.search) {
            qb.andWhere(
                '(transaction.description ILIKE :search OR transaction.notes ILIKE :search)',
                { search: `%${query.search}%` }
            );
        }
    }

    private calculateBasicStats(transactions: Transaction[]): { total_income: number; total_expense: number } {
        return transactions.reduce((acc, transaction) => {
            if (transaction.type === TransactionType.INCOME) {
                acc.total_income += transaction.amount;
            } else {
                acc.total_expense += transaction.amount;
            }
            return acc;
        }, { total_income: 0, total_expense: 0 });
    }

    private calculateCategoryStats(transactions: Transaction[], totalAmount: number): CategoryStatisticsDto[] {
        const categoryMap = new Map<string, CategoryStatisticsDto>();

        transactions.forEach(transaction => {
            if (!transaction.category_id || !transaction.category) {
                return; // Skip transactions without category
            }

            const categoryId = transaction.category_id;
            if (!categoryMap.has(categoryId)) {
                categoryMap.set(categoryId, {
                    category_id: categoryId,
                    category_name: transaction.category.name,
                    total_amount: 0,
                    transaction_count: 0,
                    percentage: 0
                });
            }

            const categoryStats = categoryMap.get(categoryId)!;
            categoryStats.total_amount += transaction.amount;
            categoryStats.transaction_count++;
        });

        // Calculate percentages
        return Array.from(categoryMap.values()).map(stat => ({
            ...stat,
            percentage: totalAmount > 0 ? Math.round((stat.total_amount / totalAmount) * 10000) / 100 : 0
        }));
    }

    private calculateBudgetStats(budgets: Budget[], transactions: Transaction[]) {
        return budgets.map(budget => {
            const categoryTransactions = transactions.filter(
                t => t.category_id === budget.category_id
            );

            const totalSpent = categoryTransactions.reduce(
                (sum, t) => sum + t.amount,
                0
            );

            const percentageUsed = budget.amount > 0 ? (totalSpent / budget.amount) * 100 : 0;

            return {
                budget_id: budget.id,
                category_id: budget.category_id,
                category_name: budget.category?.name || 'Unknown Category',
                budget_amount: budget.amount,
                spent_amount: totalSpent,
                remaining_amount: budget.amount - totalSpent,
                transaction_count: categoryTransactions.length,
                percentage_used: Math.round(percentageUsed * 100) / 100,
                is_exceeded: totalSpent > budget.amount,
                transactions: categoryTransactions.map(t => ({
                    id: t.id,
                    amount: t.amount,
                    transaction_date: t.transaction_date,
                    description: t.description
                }))
            };
        });
    }

    private getEmptyStatistics(): TransactionStatisticsDto {
        return {
            total_income: 0,
            total_expense: 0,
            net_amount: 0,
            transaction_count: 0,
            average_amount: 0,
            categories: []
        };
    }

    private getEmptyBudgetStatistics(month: number, year: number) {
        return {
            month,
            year,
            total_budget: 0,
            total_spent: 0,
            remaining_budget: 0,
            overall_percentage: 0,
            categories: []
        };
    }

    private getMonthDateRange(month: number, year: number): { startDate: Date; endDate: Date } {
        const startDate = new Date(year, month - 1, 1);
        const endDate = new Date(year, month, 0, 23, 59, 59, 999);
        return { startDate, endDate };
    }
} 